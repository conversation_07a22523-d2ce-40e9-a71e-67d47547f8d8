<?php

namespace app\service\elasticsearch;

/**
 * 创建es mapping
 * Class Document
 * @package App\Services\Elasticsearch
 */

use \app\service\elasticsearch\types\Document as TDocument;
use think\facade\Log;

class Document
{
    private $client;

    private $document;

    /**
     * 创建mapping
     * @param array $data
     * @return array
     */
    public function __construct()
    {
        $this->client = Client::getInstance();
    }

    public function setDocument(TDocument $document)
    {
        $this->document = $document;
        return $this;
    }

    public function create()
    {
        $index = $this->document->getIndex();
        $type = $this->document->getType();
        $hit = $this->document->getHit();
        $id = $this->document->getId();
        $data = [
            'index' => $index,
            'type' => $type,
            'body' => $hit,
            'id' => $id
        ];
        $result = $this->client->create($data);
        return $result;
    }

    public function update()
    {
        $index = $this->document->getIndex();
        $type = $this->document->getType();
        $hit = $this->document->getHit();
        $id = $this->document->getId();
        $data = [
            'index' => $index,
            'type' => $type,
            'id' => $id,
            'body' => ['doc' =>$hit],
        ];
        $result = $this->client->update($data);
        return $result;
    }

    public function delete()
    {
        $index = $this->document->getIndex();
        $type = $this->document->getType();
        $id = $this->document->getId();

        $data = [
            'index' => $index,
            'type' => $type,
            'id' => $id
        ];
        $result = $this->client->delete($data);

        return $result;
    }

    public function bulk(array $documents)
    {
        if (empty($documents)) {
            return [];
        }
        $body = [];
        foreach ($documents as $document) {

            $operate = $document->getOperate();
            switch ($operate) {
                case "create":
                    array_push($body, $this->getBulkIndex($document));
                    array_push($body, $this->getBulkIndexHit($document));
                    break;
                case "update":
                    array_push($body, $this->getBulkUpdate($document));
                    array_push($body, $this->getBulkUpdateHit($document));
                    break;
                case "delete":
                    array_push($body, $this->getBulkDelete($document));
                    break;
                default:
                    array_push($body, $this->getBulkIndex($document));
                    array_push($body, $this->getBulkIndexHit($document));
            }
        }

//        Log::write('***********bulk请求body*********', json_encode($body));
        $data['body'] = $body;
        $client = Client::getInstance();
        $response = $client->bulk($data);
//        Log::write('***********bulk请求返回************', json_encode($response));
    }

    private function getBulkIndex(TDocument $document)
    {

        $index = $document->getIndex();
        $type = $document->getType();
        $id = $document->getId();

        return [
            'index' => [
                '_index' => $index,
                '_type' => $type,
                '_id' => $id,
            ]
        ];
    }

    private function getBulkUpdate(TDocument $document)
    {
        $index = $document->getIndex();
        $type = $document->getType();
        $id = $document->getId();
        return [
            'update' => [
                '_index' => $index,
                '_type' => $type,
                '_id' => $id,
            ]
        ];
    }

    private function getBulkDelete(TDocument $document)
    {
        $index = $document->getIndex();
        $type = $document->getType();
        $id = $document->getId();
        return [
            'delete' => [
                '_index' => $index,
                '_type' => $type,
                '_id' => $id,
            ]
        ];
    }

    private function getBulkIndexHit(TDocument $document)
    {
        $hit = $document->getHit();

        if (is_object($hit)) {// object 转为 数组
            $hit = json_decode(json_encode($hit), true);
        }

        return $hit;
    }

    private function getBulkUpdateHit(TDocument $document)
    {
        $hit = $document->getHit();
        return [
            'doc' => $hit
        ];
    }
}
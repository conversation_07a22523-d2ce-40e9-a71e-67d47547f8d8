<?php
// 应用公共文件

use think\Response;

if (!function_exists('throwResponse')) {
    /**
     * 接口获取返回方法
     * @param int $code 状态码
     * @param string $error_msg 错误信息
     * @param array $options 返回参数
     * @param array $header 返回头信息
     * @return Response
     */
    function throwResponse($options = [], int $code = 0, string $error_msg = '', array $header = []): Response
    {
        $data['error_code']  = $code;
        $data['error_msg']  = $error_msg;
        $data['data'] = $options;
        return Response::create($data, 'json', 200)->header($header);
    }
}


if (!function_exists('dump')) {
    /**
     * 浏览器友好的变量输出
     * @param mixed $vars 要输出的变量
     * @return void
     */
    function dump($vars)
    {
        ob_start();
        var_dump($vars);

        $output = ob_get_clean();
        $output = preg_replace('/\]\=\>\n(\s+)/m', '] => ', $output);

        if (PHP_SAPI == 'cli') {
            $output = PHP_EOL . $output . PHP_EOL;
        } else {
            if (!extension_loaded('xdebug')) {
                $output = htmlspecialchars($output, ENT_SUBSTITUTE);
            }
            $output = '<pre>' . $output . '</pre>';
        }

        echo $output;
    }
}

/**
 * @Describe:检验传入参数
 * <AUTHOR>
 * @Date 2021/9/7 16:31
 * @param $data 检验数据
 * @param $rule 检验规则
 * @param $message 错误提示
 * @return array|string
 */
function checkParam(array $data,array $rule,array $message){
    $validate = \think\facade\Validate::rule($rule);
    $validate->message($message);
    if($validate->check($data)){
        return true;
    }else{
        $returnData['data'] = [];
        $returnData['error_code'] = '-1';
        $returnData['error_msg'] = $validate->getError();
        return $returnData;
    }
}

/**
 * 数组转化字符串
 * @param $array 需要转化的数组
 * @param $p 分隔符","
 * @return string
 */
function arrayToStr($array, $p = ','){
    return implode($p, $array);
}

/**
 * @param $string 需要分割的字符串
 * @param string $p 分割符","
 * @return false|string[]
 */
function strToArray($string, $p = ','){
    return explode($p, $string);
}

/**
 * @Describe:
 * <AUTHOR>
 * @Date 2022/3/4 14:18
 * @param $url
 * @param $data
 * @return bool|string
 */
function http_post_json($url,$data){
    if (empty($url) || empty($data)) {
        return false;
    }
    $curl = curl_init($url); //请求的URL地址
    curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($curl, CURLOPT_POSTFIELDS, $data);//$data JSON类型字符串
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt( $curl, CURLOPT_SSL_VERIFYPEER, false );//顶顶审批必须开启
    curl_setopt( $curl, CURLOPT_SSL_VERIFYHOST, false );//顶顶审批必须开启
    curl_setopt($curl, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Content-Length: ' . strlen($data)));
    $output = curl_exec($curl);
    curl_close($curl);
    return $output;
}

/**
 * @Describe:审核内容
 * <AUTHOR>
 * @Date 2022/4/25 17:27
 */
function examine($data){
    $url = env('ITEM.AUDIT_URL').'/contentaudit/v3/comment/add';
    $data = json_encode($data,JSON_UNESCAPED_UNICODE);
    $result = http_post_json($url,$data);
//    echo json_encode($data);
//    var_dump($data,$result);
    if(!empty($result)){
        $code =  json_decode($result,true);

        if($code){
            return json_decode($result,true);
        }
        $results['error_code'] = -1;
        $results['error_msg'] = "敏感词接口，调用失败";
        return $results;
    }else{
        $results['error_code'] = -1;
        return $results;
    }
}

/**
 * @Describe:审核敏感词
 * <AUTHOR>
 * @Date 2022/4/25 17:27
 */
function examineWord($text){
    $url = env('ITEM.BADWORDS_URL').'/v3/vinehoo.commit/badwords/validate'.'';
    $data['text'] = $text;
    $data = json_encode($data,JSON_UNESCAPED_UNICODE);
    $result = http_post_json($url,$data);
    if(!empty($result)){
        return json_decode($result,true);
    }else{
        $result['error_code'] = -1;
        return $result;
    }
}

/**
 * @Describe:获取话题详情
 * <AUTHOR>
 * @Date 2022/4/26 17:51
 */
function getTopicInfo($id){
    $url = env('ITEM.COMMUNITY_URL').'/community/v3/topic/detail?id='.$id;
    $result = file_get_contents($url);
    if(!empty($result)){
        return json_decode($result,true);
    }else{
        $result['error_code'] = -1;
        return $result;
    }
}
/**
 * http post请求，参数为json字符串
 * @param $url
 * @param $data_string
 * @return bool|string
 */
function httpPostString(string $url, string $data_string, $header = '')
{
    if (empty($header)) {
        $header = array(
            'Content-Type: application/json',
            "vinehoo-client: news",
        );
    }
    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL            => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING       => "",
        CURLOPT_MAXREDIRS      => 10,
        CURLOPT_TIMEOUT        => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST  => "POST",
        CURLOPT_POSTFIELDS     => $data_string,
        CURLOPT_HTTPHEADER     => $header
    ));
    $response = curl_exec($curl);
    $err      = curl_error($curl);
    curl_close($curl);
    if ($err) {
        \think\facade\Log::error($url . '调用失败：' . $err . '调用参数：' . $data_string);
        return ['error_code' => 10002, 'error_msg' => $err, 'data' => []];
    } else {
        if (is_null(json_decode($response))) return $response;
        return json_decode($response, true);
    }
}

/**
 * 图片地址补全
 * @param $imgurl
 * @return string
 */
function imagePrefix($imgurl)
{
    if (!empty($imgurl)) {
        $image = explode(",",$imgurl);
        $img_url = [];
        foreach ($image as $val){
            if (strpos($val, 'http') === false) {
                if (substr($val, 0, 1) == '/') {
                    $img_url[] = env('ALIURL') . $imgurl;
                } else {
                    $img_url[] = env('ALIURL') . '/' . $imgurl;
                }
            }
            $imgurl = implode(",",$img_url);
        }
    }
    return $imgurl;
}

/**
 * @param $uids
 * @param $header
 * @return array
 */
function getUserInfoByUid($uids){
    //请求地址
    $base=env('ITEM.USER_URL');
    $url=$base.'/user/v3/profile/getUserInfo';
    $body=[
        'uid'=>$uids,
    ];
    $userInfo=curlRetryRequest($url,json_encode($body,true),[],"POST");
    $userInfos=[];
    if ((isset($userInfo['data']) && !empty($userInfo['data']))) {
        $data=$userInfo['data']['list'];
        foreach ($data as $v){
            if (empty($v['uid'])){
                continue;
            }
            $userInfos[$v['uid']]=$v;
        }
        return  $userInfos;
    }
    return  $userInfos;
}

/**
 * @方法描述:url请求重试
 * @param string $url 请求url
 * @param array $data 请求参数
 * @param array $haeder 请求头
 * @param string $method 请求的方式
 * @param int $timeout 超时时间，单位s
 * @return Response
 */
function curlRetryRequest($url, $data = [], $haeder = [], $method = 'POST', $timeout = 3, $i = 0)
{
    $res = curlRequest($url, $data, $haeder, $method, $timeout) ?? '';
    $res = json_decode($res,true);
    if (!isset($res['error_code']) && $i<3) {
        $i++;
        $res = curlRetryRequest($url, $data, $haeder, $method, $timeout, $i);
    }
    return $res;
}

/**
 * @方法描述:url请求
 * @param string $url 请求url
 * @param array $data 请求参数
 * @param array $haeder 请求头
 * @param string $method 请求的方式
 * @param int $timeout 超时时间，单位s
 * @param bool $sync 不启用异步，默认为true
 * @return Response
 */
function curlRequest($url, $data = [], $haeder = [], $method = 'POST', $timeout = 10, $sync = True)
{
    $ch = curl_init();
    if (is_array($data)) {
        $data = http_build_query($data);
    }
    if (empty($haeder)) {
        $haeder = array(
            "content-type: application/json",
            "vinehoo-client: community",
        );
    }
    if ($method == 'POST') {
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, True);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    } else {
        if (!empty($data)) curl_setopt($ch, CURLOPT_URL, "{$url}?{$data}");
        else curl_setopt($ch, CURLOPT_URL, $url);
    }
    if (strlen($url) > 5 && strtolower(substr($url, 0, 5)) == "https") {
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    }
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    if ($haeder) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $haeder);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, $sync);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_NOBODY, !$sync);
    $return = curl_exec($ch);
    curl_close($ch);
    return $return;
}

/**
 * Description:日期友好显示新规则
 * Author: zrc
 * Date: 2021/5/17
 * Time: 15:08
 * @param null $time
 * @return false|string
 */
function mdate($time = NULL)
{
    if (is_string($time)) {
        $time = strtotime($time);
    }
    $text = '';
    $time = $time === NULL || $time > time() ? time() : intval($time);
    $t = time() - $time; //时间差 （秒）
    $y = date('Y', $time) - date('Y', time());//是否跨年

    switch ($t) {
        case $t < 60 * 10:
            $text = '刚刚';
            break;
        case $t < 60 * 60:
            $text = floor($t / 60) . '分钟前'; //一小时内
            break;
        case $t < 60 * 60 * 24:
            $text = floor($t / (60 * 60)) . '小时前'; // 一天内
            break;
        case $t < 60 * 60 * 24 * 7:
            $text = floor($t / (60 * 60 * 24)) . '天前'; //几天前
            break;
        case $t < 60 * 60 * 24 * 30:
            //$text = floor($t / (60 * 60 * 24 * 7)) . '周前';
            $text = '1周前'; // 几周前(统一都显示1周前)
            break;
        case $t < 60 * 60 * 24 * 365:
            //case $t < 60 * 60 * 24 * 365 && $y == 0:
            //$text = floor($t / (60 * 60 * 24 * 30)) . '月前';
            $text = '1月前'; //几月前(统一都显示1月前)
            break;
        default:
            //$text = floor($t / (60 * 60 * 24 * 365)) . '年前';
            $text = '1年前';//几年前(统一都显示1年前)
            break;
    }
    return $text;
}
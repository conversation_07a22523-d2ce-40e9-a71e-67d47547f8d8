<?php
declare (strict_types=1);

namespace app\service\command;


use app\service\elasticsearch\Document;
use app\service\elasticsearch\types\WineAcademyDocument;
use think\facade\Db;


class BulkSynAcademyToEsCommand
{
    private $wineAcademyModel;
    private $document;

    private function init()
    {
        $this->document = new Document();
    }

    public function exec($model)
    {
        $this->init();
        $this->wineAcademyModel = $model;

        $fields = [
            'id',
            'price',
            'title',
            'city_name',
            'deadline'
        ];

        $data = $this->wineAcademyModel->field($fields)
            ->where(['status' => 1])
            ->where('deadline','>=',time())
            ->order('add_time desc')
            ->select()->toArray();

        $documents = [];

        foreach ($data as $item) {
            $item['resource'] = 'academy';
            $item['effect_time_range'] = ['gte' => 0, 'lte' => $item['deadline']];
            unset($item['deadline']);
            $wineAcademyDocument = new WineAcademyDocument($item['id']);
            $operate = !empty($reWaitSysData[$item['id']]) ? $reWaitSysData[$item['id']]['operate'] : 'create';
            $wineAcademyDocument->setIndex(env('ES.PREFIX').'academy_'.env('ES.ENV'));
            $wineAcademyDocument->setOperate($operate);
            $wineAcademyDocument->setHit($item);
            $wineAcademyDocument->toArray();
            $documents[] = $wineAcademyDocument;
        }
        $this->document->bulk($documents);
    }
}

<?php
declare (strict_types=1);

namespace app\service\command;


use app\service\elasticsearch\Document;
use app\service\elasticsearch\types\WeiboDocument;
use think\facade\Db;


class BulkSynInvitationToEsCommand
{
    private $weiboModel;
    private $document;

    private function init()
    {
        $this->document = new Document();
    }

    public function exec($model)
    {
        $this->init();
        $this->weiboModel = $model;

        $fields = [
            'a.weibo_id',
            'a.content',
            'a.address',
            'a.wename',
            'a.is_best',
            'a.istop',
            'b.title',
        ];

        $documents = [];
        Db::name('weibo')
            ->alias('a')
            ->field($fields)
            ->leftJoin('weibo_topic b', 'a.topicids = b.id')
            ->where('a.isdel=0')
            ->chunk(1000, function ($weibo)use($documents) {
                foreach ($weibo as $item) {
                    $item['resource'] = 'weibo';
                    $item['effect_time_range'] = ['gte' => 0, 'lte' => 4747881840];
                    $item['id'] = $item['weibo_id'];
                    $weiboDocument = new WeiboDocument($item['weibo_id']);
                    $operate = !empty($reWaitSysData[$item['weibo_id']]) ? $reWaitSysData[$item['weibo_id']]['operate'] : 'create';
                    $weiboDocument->setIndex(env('ES.PREFIX') . 'weibo_' . env('ES.ENV'));
                    $weiboDocument->setOperate($operate);
                    $weiboDocument->setHit($item);
                    $weiboDocument->toArray();
                    $documents[] = $weiboDocument;
                }
                $this->document->bulk($documents);
            });

//        $data = $this->weiboModel->alias('a')
//            ->field($fields)
//            ->leftJoin('weibo_topic b','a.topicids = b.id')
//            ->where('a.isdel=0')
//            ->order(['a.istop'=>'desc','a.ctime'=>'desc'])
//            ->select()->toArray();

//        $documents = [];
//
//        foreach ($data as $item) {
//            $item['resource'] = 'weibo';
//            $item['effect_time_range'] = ['gte' => 0, 'lte' => 4747881840];
//            $item['id'] = $item['weibo_id'];
//            $weiboDocument = new WeiboDocument($item['weibo_id']);
//            $operate = !empty($reWaitSysData[$item['weibo_id']]) ? $reWaitSysData[$item['weibo_id']]['operate'] : 'create';
//            $weiboDocument->setIndex(env('ES.PREFIX').'weibo_'.env('ES.ENV'));
//            $weiboDocument->setOperate($operate);
//            $weiboDocument->setHit($item);
//            $weiboDocument->toArray();
//            $documents[] = $weiboDocument;
//        }
//
//        $this->document->bulk($documents);
    }
}

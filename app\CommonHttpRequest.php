<?php
declare (strict_types = 1);

namespace app;
use GuzzleHttp\Exception\RequestException;
use think\facade\Log;

/**
 * 自定义请求类
 */
class CommonHttpRequest
{
    public function httpRequest(string $url, array $params = [], string $method = 'POST', array $configs = [], string $contentType = 'form_params')
    {
        $timeout = env('TIMEOUT',5);
        $configs['timeout'] = $configs['timeout'] ?? $timeout;
        $client = new \GuzzleHttp\Client($configs);
        $params = strtoupper($method) == 'GET' ? ['query' => $params] : [$contentType => $params];

        try {
            $request = $client->request($method, $url, $params);
            $return = $request->getBody()->getContents();
        } catch (RequestException $e) {
            $message = $e->getMessage();
            $return = [
                'status' =>'fail',
                'errorCode'=>'-1',
                'msg'=>$message,
                'data'=> []
            ];
            //请求失败记录日志信息
            $this->recordErrorLog($message);
        }

        if(!is_array($return)){
            $response = json_decode($return, true);
        }else{
            $response = $return;
        }

        return $response;
    }

    public function httpGet(string $url, array $params = [], array $configs = [])
    {

        $timeout = env('TIMEOUT',5);
        $configs['timeout'] = $configs['timeout'] ?? $timeout;
        $client = new \GuzzleHttp\Client($configs);
        $params = ['query' => $params];

        try {
            $request = $client->request('GET', $url, $params);
            $return = $request->getBody()->getContents();
        } catch (RequestException $e) {
            $message = $e->getMessage();
            $return = [
                'status' =>'fail',
                'errorCode'=>'-1',
                'msg'=>$message,
                'data'=> []
            ];
            //请求失败记录日志信息
            $this->recordErrorLog($message);
        }

        if(!is_array($return)){
            $response = json_decode($return, true);
        }else{
            $response = $return;
        }

        return $response;
    }

    public function httpPost(string $url, array $params = [], array $configs = [], string $contentType = 'form_params')
    {
        $timeout = env('TIMEOUT',5);
        $configs['timeout'] = $configs['timeout'] ?? $timeout;
        $client = new \GuzzleHttp\Client($configs);
        $params = [$contentType => $params];

        try {
            $request = $client->request('POST', $url, $params);
            $return = $request->getBody()->getContents();
        } catch (RequestException $e) {
            $message = $e->getMessage();
            $return = [
                'status' =>'fail',
                'errorCode'=>'-1',
                'msg'=>$message,
                'data'=> []
            ];
            //请求失败记录日志信息
            $this->recordErrorLog($message);
        }

        if(!is_array($return)){
            $response = json_decode($return, true);
        }else{
            $response = $return;
        }

        return $response;
    }


    /**
     * 请求失败记录日志
     * @param $e 异常信息
     */
    private function recordErrorLog($errorInfo)
    {
        Log::record($errorInfo,'error');
    }
}
<?php

namespace app\service\intelligent;

use think\facade\Db;

/**
 * Class ArticleContent
 * @package App\Services\intelligent
 */
class AcademyContent extends Content
{
    private $ids = [];
    private $type = 'academy';
    private $input = [];

    public function getType()
    {
        return $this->type;
    }

    public function addId($id)
    {
        array_push($this->ids, $id);
    }

    public function setInput($input)
    {
        array_push($this->input, $input);
    }

    //返回数据
    public function getList()
    {
        if (empty($this->input)) {
            return [];
        }

        $fields = [
            'id',
            'cover_img',
            'city_id',
            'if(limit_num>0,limit_num-regist_num,-1) as remain_num',
        ];

        $dataExt = Db::name('wine_academy')
            ->field($fields)
            ->whereIn('id', $this->ids)
            ->select()->toArray();

        $dataExt = array_column($dataExt, null, 'id');

        $data = $this->input;
        foreach ($data as &$item) {
            if (empty($dataExt[$item['id']])) {
                continue;
            }
            $item['cover_img'] = pictureDomain($dataExt[$item['id']]['cover_img']);
            $item['city_id'] = $dataExt[$item['id']]['city_id'];
            $item['remain_num'] = $dataExt[$item['id']]['remain_num'];
            unset($item['effect_time_range']);
        }
        return $data;
    }
}
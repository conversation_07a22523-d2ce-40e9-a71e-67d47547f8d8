<?php


namespace app\service\intelligent;

use think\facade\Db;

class CareasContent extends Content
{
    private $ids = [];
    private $type = 'careas';
    private $input = [];

    public function getType()
    {
        return $this->type;
    }

    public function addId($id)
    {
        array_push($this->ids, $id);
    }

    public function setInput($input)
    {
        array_push($this->input, $input);
    }

    //返回数据
    public function getList()
    {
        if (empty($this->input)) {
            return [];
        }

        $fields = [
            'cid as id',
            'field8',
            'message',
            'evaluation',
        ];

        $dataExt = Db::name('careas')
            ->field($fields)
            ->where(['isdel'=>0])
            ->whereIn('cid', $this->ids)
            ->order('sort','desc')
            ->select()->toArray();

        $dataExt = array_column($dataExt, null, 'id');

        $data = $this->input;
        foreach ($data as $k=>&$item){
            if (empty($dataExt[$item['id']])) {
                unset($data[$k]);continue;
            }
            $item['img'] = $dataExt[$item['id']]['field8'];
            $item['message'] = $dataExt[$item['id']]['message'];
        }
        return $data;
    }
}
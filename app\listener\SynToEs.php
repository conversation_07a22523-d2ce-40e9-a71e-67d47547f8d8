<?php
declare (strict_types=1);

namespace app\listener;



use app\model\SynEsModel;
use think\facade\Log;

class SynToEs
{
    /**
     * 事件监听处理
     *
     * @return mixed
     */
    public function handle($event)
    {
        $data = [
            'task_id' => $event['id'],
            'type' => $event['type'],
            'operate' => $event['operate']
        ];
        $synEsModel = new SynEsModel();
        $synEsModel->where(['task_id' => $event['id'],'type' => $event['type']])->delete();
        $res = $synEsModel->insert($data);
        if(!$res){
            Log::info('同步数据到es失败{task_id}',['task_id' => $event['id']]);
        }
    }
}

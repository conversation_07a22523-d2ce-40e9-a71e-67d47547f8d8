<?php
namespace app\model;

use app\service\admin\ArticleService;
use think\facade\Db;
use think\facade\Log;
use think\Model;

/**
 * 酒闻资讯
 */
class ArticleModel extends Model {
    protected $name='article';
    protected $pagesize = 10;//默认分页数量
    protected $pagenumber = 1;//默认页码

    /**
     * 酒闻列表
     *
     * @param array $param 查询条件参数
     * @return array $res 返回信息
     */
    public function getArticleList($param){
        if(isset($param['limit'])){
            $pagesize = $param['limit']?$param['limit']:$this->pagesize;//分页数量
        }
        if(isset($param['page'])){
            $pagenumber = $param['page']?$param['page']:$this->pagenumber;//页码
        }

//        var_dump($param);
        if(isset($param['keywords']) && strlen($param['keywords']) > 0) {
            $where []= ['title','like','%'.trim($param['keywords']).'%'];
            $where []= ['status','=',1];
            $where []= ['cstatus','<>',-1];
        }else{
            $where []= ['status','=',1];
            $where []= ['cstatus','<>',-1];
        }

        $startpagesize = $pagesize * ($pagenumber - 1);//起始位置

        /*if(!empty($param['uid'])){
            $fieldWhere = "and (uid={$param['uid']} or is_show=1)";
        }else{
            $fieldWhere = "and is_show=1 ";
        }*/
        $fieldWhere = "and is_show=1 ";
        $res = Db::name('article')
            ->alias('a')
//            ->field("a.id,a.title,a.img,a.abst,a.viewnums,a.source,(SELECT count(*) from vh_article_comment as c WHERE c.aid=a.id {$fieldWhere}) as commentnums")
            ->field("a.id,a.title,a.img,a.abst,a.viewnums,a.source,commentnums")
//            ->where("a.status=1 and a.cstatus!=-1")
            ->where($where)
//            ->order(['a.is_index'=>'desc','a.ordid'=>'desc','a.updatetime'=>'desc'])
            ->order(['a.ordid'=>'desc','a.updatetime'=>'desc'])
            ->limit($startpagesize,$pagesize)->select()->toArray();

        $totalNum =  Db::name('article')->where($where)->count();

        $totalPage = (int)ceil($totalNum / $pagesize);

        $this->articleViewInc(array_column($res, 'id')); //本次列表取出的全部文章 浏览量+1 @cct 2022年8月8日16:47:35

        $result['list'] = $res;
        //$result['totalPage'] = $totalPage;
        $result['page'] = $pagenumber;
        $result['total'] = $totalNum;
        $result['limit'] = $pagesize;

        return $result;
    }

    /**
     * @方法描述:指定文章的浏览量 +1
     * <AUTHOR>
     * @Date 2022/8/8 17:20
     * @param $ids array 文章IDS
     * @throws \think\db\exception\DbException
     */
    protected function articleViewInc($ids)
    {
        if (!empty($ids)) {
            $ids = array_map(function ($v) {
                return intval($v);
            }, $ids); //过滤一下类型.
            $res = Db::name('article')->whereIn('id', $ids)->inc('viewnums', 1)->update();
            if ($res !== count($ids)) {
                $sql = Db::getLastSql();
                Log::error("酒闻列表更新浏览量失败: " . json_encode([
                        'param' => $ids,
                        'res'   => $res,
                        'sql'   => $sql,
                    ]));
            };
        }
    }


    /**
     * 酒闻详情
     *
     * @param array $id 资讯id
     * @return array $res 返回信息
     */
    public function getArticleDetails($id){
        $res = Db::name('article')
        ->alias('a')
        ->leftJoin('article_cate b','a.cate_id = b.id')
        ->field('a.topic_id,a.topic_name,a.id,a.title,a.is_hot,a.img,a.abst,a.viewnums,a.source,a.period,(SELECT count(*) from vh_article_comment as c WHERE c.aid=a.id) as commentnums,a.info,a.md_info,b.name as catename,add_time')
        ->where('a.id',$id)
        ->where("a.status=1 and a.cstatus!=-1")->find();

        return $res;
    }


    /**
     * 查询是否点赞
     *
     * @param array $id 资讯id
     * @param array $uid 用户id
     * @return array $res 返回信息
     */
    public function getCollectInfo($id,$uid){
        $res = Db::name('article_collect')->where(['articleid'=>$id,'uid'=>$uid])->find();

        return $res;
    }


    /**
     * 更新资讯相关信息
     *
     * @param array $id 资讯id
     * @param string $files 更新字段
     * @param int $num 改变数量
     * @return array $res 返回信息
     */
    public function updateArticleInfo($id,$files,$num){
        $res = Db::name('article')->where("id",$id)->inc($files,$num)->update();
        (new ArticleService)->createJson($id);
        return $res;
    }


    /**
     * 添加收藏资讯
     *
     * @param array $id 资讯id
     * @param array $uid 用户id
     * @return array $res 返回信息
     */
    public function AddCollectArticle($id,$uid){
        $info = array(
            'articleid' => $id, //资讯id
            'uid' => $uid, //用户id
            'create_time'=>time()
        );
        $res = Db::name('article_collect')->insert($info);
        return $res;
    }


    /**
     * 取消收藏资讯
     *
     * @param array $id 资讯id
     * @param array $uid 用户id
     * @return array $res 返回信息
     */
    public function delCollectArticle($id,$uid){
        $where[] = ['articleid','in',$id];
        $where[] = ['uid','=',$uid];
        $res = Db::name('article_collect')->where($where)->delete();
        return $res;
    }


    /**
     * 查询资讯对应的评论列表
     *
     * @param array $param 查询参数
     * @return array $res 返回结果
     */
    public function getArticleComment($param){
        if(isset($param['limit'])){
            $pagesize = $param['limit']?$param['limit']:$this->pagesize;//分页数量
        }
        if(isset($param['page'])){
            $pagenumber = $param['page']?$param['page']:$this->pagenumber;//页码
        }
        $startpagesize = $pagesize * ($pagenumber - 1);//起始位置

        if(!empty($param['uid'])){
            $whereRaw = "(uid={$param['uid']} or is_show=1) and audit_status <> 3 ";
        }else{
            $whereRaw = "is_show=1";
        }

//        $where = ['aid'=>$param['id'],'recomid'=>0,"is_show"=>1];
        $where = ['aid'=>$param['id'],'recomid'=>0];

        $res = Db::name('article_comment')->field('comid,uid,boxid,reuid,content,address,diggnums,emoji_image,FROM_UNIXTIME(addtime) as addtime')
        ->where($where)
        ->whereRaw($whereRaw)
        ->order(['hot_vaule'=>'desc','addtime'=>'desc'])
        ->limit($startpagesize,$pagesize)->select()->toArray();

        $totalNum = Db::name('article_comment')->where($where)->count();
//        $totalPage = (int)ceil($totalNum / $pagesize);
        $result['list'] = $res;
        //$result['totalPage'] = $totalPage;
        $result['page'] = $pagenumber;
        $result['total'] = $totalNum;
        $result['limit'] = $pagesize;
        
        return $result;        
    }


    /**
     * 查询评论回复的评论信息
     *
     * @param array $boxidarr 资讯的评论分块id
     * @param string $uid 用戶id
     * @return array $res 返回结果
     */
    public function getArticleReplyComment($boxidarr,$uid=""){
        if(!empty($uid)){
            $whereRaw = "(uid={$uid} or is_show=1) and audit_status <> 3";
        }else{
            $whereRaw = "is_show=1";
        }
        $res = Db::name('article_comment')->field('comid,uid,content,address,diggnums,emoji_image,FROM_UNIXTIME(addtime) as addtime,recomid,boxid,reuid')
        ->where("recomid!=0")
//        ->where("is_show=1")//只显示评论审核通过的
        ->whereRaw($whereRaw)
        ->where('boxid','in',$boxidarr)
        ->order(['hot_vaule'=>'desc','addtime'=>'desc'])->select()->toArray();
        return $res;
    }


    /**
     * 查询评论回复的评论信息
     *
     * @param int $id 资讯的评论id
     * @return array $res 返回结果
     */
    public function getComment($id){
        $res = Db::name('article_comment')->where("comid",$id)->find();
        return $res;
    }


    /**
     * 批量查询文章评论点赞信息
     *
     * @param int $wherestr 查询条件
     * @return void
     */
    public function getBatchDigg($wherestr){
        //查询用户是否已经点过赞
        $is_digg = Db::name('article_digg_log')->where($wherestr)->select()->toArray();
        return $is_digg;
    }

    
    /**
     * 资讯点赞记录日志
     *
     * @param array $log 日志参数
     * @return void
     */
    public function InsertArticleLog($log){
        $flag = 0;
        // 启动事务
        Db::startTrans();
        try {
            Db::name('article_log')->insert($log);//插入数据
            //更新赞次数
            Db::name('article')->where('id', $log['aid'])->inc('diggnums')->update();

            // 提交事务
            Db::commit();
            $flag = 1;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
        }

        if($flag == 1){
            return true;
        }else{
            return false;
        }

    }


    /**
     * 资讯评论点赞记录日志
     *
     * @param array $log 日志参数
     * @return void
     */
    public function InsertCommentLog($log){
        $flag = 0;
        // 启动事务
        Db::startTrans();
        try {
            Db::name('article_digg_log')->insert($log);//插入数据
            //更新赞次数
            Db::name('article_comment')->where('comid', $log['comid'])->inc('diggnums')->update();

            // 提交事务
            Db::commit();
            $flag = 1;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
        }

        if($flag == 1){
            return true;
        }else{
            return false;
        }

    }


    /**
     * 查询评论回复的评论信息
     *
     * @param int $id 资讯的评论id
     * @param int $uid 用户id
     * @return array $res 返回结果
     */
    public function getArticleLog($id,$uid){
        $res = Db::name('article_log')->where(['aid'=>$id,'uid'=>$uid])->find();
        return $res;
    }


    /**
     * 查询评论点赞
     *
     * @param int $id 资讯的评论id
     * @param int $uid 用户id
     * @return array $res 返回结果
     */
    public function getCommentLog($id,$uid){
        $res = Db::name('article_digg_log')->where(['comid'=>$id,'uid'=>$uid])->find();
        return $res;
    }


    /**
     * 酒闻收藏列表
     *
     * @param array $param 查询条件参数
     * @param int $uid 用户id
     * @return array $res 返回信息
     */
    public function getMyArticleCollectList($param,$uid){
        if(isset($param['limit'])){
            $pagesize = $param['limit']?$param['limit']:$this->pagesize;//分页数量
        }
        if(isset($param['page'])){
            $pagenumber = $param['page']?$param['page']:$this->pagenumber;//页码
        }
        $startpagesize = $pagesize * ($pagenumber - 1);//起始位置


        $res = Db::name('article_collect')
        ->alias('a')
        ->leftJoin('article b','a.articleid = b.id')
        ->field("a.create_time,b.id,b.title,b.img,b.abst,b.viewnums,(SELECT count(*) from vh_article_comment as c WHERE c.aid=b.id and (uid = {$uid} or is_show = 1)) as commentnums")
        ->where('a.uid',$uid)
        ->where("b.status=1 and b.cstatus!=-1")
        ->order(['a.create_time'=>'desc'])
//        ->order(['b.add_time'=>'desc'])
        ->limit($startpagesize,$pagesize)->select()->toArray();

        foreach ($res as $k=>$v){
            $res[$k]['create_time'] = date("Y-m-d H:i:s",$v['create_time']);
        }

        $totalNum =  Db::name('article_collect')
        ->alias('a')
        ->leftJoin('article b','a.articleid = b.id')
        ->where('a.uid',$uid)
        ->where("b.status=1 and b.cstatus!=-1")->count();

        $totalPage = (int)ceil($totalNum / $pagesize);
        $result['list'] = $res;
        //$result['totalPage'] = $totalPage;
        $result['page'] = $pagenumber;
        $result['total'] = $totalNum;
        $result['limit'] = $pagesize;

        return $result;
    }


    /**
     * 添加到评论分块表
     *
     * @param array $temp 入库参数
     * @return void
     */
    public function addBox($temp,$save){        
        // 启动事务
        $flag = 0;


        $boxid = Db::name('comment_box')->insertGetId($temp);
        $save['boxid']=empty($boxid)?0:$boxid;//评论分块id
        $comment_id = $this->addComment($boxid,$save);//评论入库

        // 提交事务
        $flag = 1;


        if($flag == 1){
            return $comment_id;
        }else{
            return false;
        }
    }


    /**
     * 修改评论分块表
     *
     * @param int $boxid 评论分块id
     * @param array $temp 入库参数
     * @return boolean $res 返回结果true/false 
     */
    public function saveBox($boxid,$temp,$save){
        // 启动事务
        $flag = 0;


            Db::name('comment_box')->where("boxid",$boxid)->save($temp);
            $save['boxid']=empty($boxid)?0:$boxid;//评论分块id
            $comment_id = $this->addComment($boxid,$save);//评论入库

            // 提交事务

            return $comment_id;

        if($flag == 1){
            return true;
        }else{
            return false;
        }
    }
   
    /**
     * 评论内容入库
     *
     * @return void
     */
    public function addComment($boxid,$save){
        //添加评论
        $id = Db::name('article_comment')->insertGetId($save);
        
        //更新最新评论id
        Db::name('comment_box')->where("boxid",$boxid)->save(['lastcomid'=>$id]);

		//更新评论次数 字段加 1
//        Db::name('article')->where('id',$save['aid'])->inc('commentnums')->update();
		
        return $id;	
    }


    /**
     * 评论信息查询
     *
     * @param array $comid 文章的评论id
     * @return array $res 返回结果
     */
    public function getIdComment($comid){
        $res = Db::name('article_comment')->field('comid,boxid,uid,recomid,content,address,diggnums,FROM_UNIXTIME(addtime) as addtime')
        ->where("comid",$comid)->find();
        return $res;
    }



    /**
     * 后台管理--酒闻列表
     *
     * @param array $param 查询条件参数
     * @return array $res 返回信息
     */
    public function getAdminArticleList($param){
        if(isset($param['limit'])){
            $pagesize = $param['limit']?$param['limit']:$this->pagesize;//分页数量
        }
        if(isset($param['page'])){
            $pagenumber = $param['page']?$param['page']:$this->pagenumber;//页码
        }
        if(!isset($param['is_index'])){
            $param['is_index'] = " ";
        }
        $startpagesize = $pagesize * ($pagenumber - 1);//起始位置

        //查询条件处理
        $where = [];
        if(!empty($param['cate_id'])){//分类
            $where[] = ['a.cate_id','=',$param['cate_id']];
        }           
        
        if(!empty($param['status'])){//状态
            if($param['status'] == 1){//已审核
                $where[] = ['a.status','=',1];
            }elseif($param['status'] == 2){//未审核
                $where[] = ['a.status','=',0];
            }           
        }

        if(isset($param['is_index'])){
            $param['is_index'] = is_numeric($param['is_index'])?intval($param['is_index']):'';
            if(!empty($param['is_index']) || $param['is_index'] === 0){//评论状态
                $where[] = ['a.is_index','=',$param['is_index']];
            }
        }



        $orderby = 'a.updatetime desc';
        if(!empty($param['stort'])){//排序
            if($param['stort'] == 1){//浏览量
                $orderby = 'a.viewnums desc';
            }elseif($param['stort'] == 2){//状态
                $orderby = 'a.status desc';
            }elseif($param['stort'] == 3){//更新时间
                $orderby = 'a.updatetime desc';
            }else{
                $orderby = 'a.updatetime desc';
            }
        } 

        if(!empty($param['keywords'])){//关键字
            $where[] = ['a.title','like', "%{$param['keywords']}%"];
        } 

        if(!empty($param['id'])){//资讯ID
            $where[] = ['a.id','=',$param['id']];
        } 
        $field = 'a.id,a.topic_id,topic_name,b.name as catename,a.abst,a.adname,a.update_name,a.title,img,a.updatetime,a.commentnums,a.viewnums,a.is_hot,a.is_index,a.status,a.ordid';
        $res = Db::name('article')
        ->alias('a')
        ->join('article_cate b','a.cate_id = b.id','left')
        ->field($field)
        ->where($where)
        ->order($orderby)
        ->limit($startpagesize,$pagesize)->select()->toArray();

        $totalNum = Db::name('article')->alias('a')->leftJoin('article_cate b','a.cate_id = b.id')->where($where)->count();

        $totalPage = (int)ceil($totalNum / $pagesize);
        $result['list'] = $res;
        //$result['totalPage'] = $totalPage;
        $result['total'] = $totalNum;

        return $result;
    }


    public function operateArticle($param){
        // 启动事务
        $flag = 0;
        Db::startTrans();
        try {
            $vote_ids = $param['vote_ids'] ?? [];
            unset($param['vote_ids']);

            if(!isset($param['id']) || empty($param['id'])){//添加
                unset($param['id']);
                $id = Db::name('article')->insertGetId($param);
                $operate = 'create';
            }else{//编辑
                Db::name('article')->save($param);
                Db::name('article_comment')->where('aid','=',$param['id'])->update(['is_show'=>$param['status']]);
                $id = $param['id'];
                $operate = 'update';
            }

            if(!empty($vote_ids)){
                Db::name('article_vote')
                    ->where('id', 'in', $vote_ids)
                    ->update(['article_id' => $id]);
            }

            if(!empty($param['wids'])){//关联酒款ID
                $this -> pushArtHonordy($param['wids'],$id,1,$param);
            }

            if(!empty($param['countrys'])){//关联国家ID
                $this -> pushArtHonordy($param['countrys'],$id,4,$param);
            }

            if(!empty($param['villages'])){//关联酒庄ID
                $this -> pushArtHonordy($param['villages'],$id,3,$param);
            }

            if(!empty($param['areas'])){//关联产区ID
                $this -> pushArtHonordy($param['areas'],$id,5,$param);
            }
            (new ArticleService)->createJson($id);
            //同步 article-es
            event('SynToEs',['id' => $id,'type'=>2,'operate'=>$operate]);
            // 提交事务
            Db::commit();
            $flag = 1;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            print_r($e->getMessage());die ;
        }
        if($flag == 1){
            return true;
        }else{
            return false;
        }
        
    }
    

    /**
     * 处理资讯关联酒庄
     * [pushArtHonordy description]
     * @param  string  $ids   [description]
     * @param  integer $artid [description]
     * @param  integer $type 1 酒款 2子酒款 3酒庄 4国家 5产区 [description]
     * @return [type]         [description]
     */
    public function pushArtHonordy($ids="",$artid=0,$type=1,$param){        
        $ids = explode(",", $ids);
        $newids = array_filter($ids);
        $artinfo = Db::name('article')->where(['id'=>$artid])->find();

        if(!empty($artinfo)){
            foreach ($newids as $k => $v) {
                $info = Db::name('honordy')->where(['wid'=>$v,'artid'=>$artid,'htype'=>$type,'type_id'=>1])->find();

                $save=array();
                $save['uid'] = $artinfo['adminid'];
                $save['user_name'] = $artinfo['adname'];
                $save['hondy_image']=$artinfo['img'];
                $save['info']=$artinfo['abst'];
                $save['honordy_name']=$artinfo['title'];
                $save['gg_time']=time();
                $save['status']=$artinfo['status'];

                if(empty($info)){
                    $idsArr = [];
                    $save['wid']=$v;
                    $save['add_time']=time();
                    $save['htype']=$type;
                    $save['type_id']=1;
                    $save['artid']=$artid;
                    $data[]=$save;
                }else{
                    $idsArr[]=$info['honorid'];
                    Db::name('honordy')->where(['honorid'=>$info['honorid']])->save($save);
                }
            }

            //删除不存在的规格
            if(!empty($idsArr)){
                $idsArr=implode(",", $idsArr);
                $res = Db::name('honordy')->where(['artid'=>$artid,'htype'=>$type])->whereNotIn('honorid',$idsArr)->select()->toArray();
                if(!empty($res)){
                    Db::name('honordy')->where(['artid'=>$artid,'htype'=>$type])->whereNotIn('honorid',$idsArr)->delete();
                }
            }else{
                Db::name('honordy')->where(['artid'=>$artid,'htype'=>$type])->delete();

                Db::name('honordy')->insertAll($data);
            }

        }
            
    }


    /**
     * 查询文章详情
     *
     * @param int $id 文章id
     * @return array $res 文章参数
     */
    public function getArticleInfo($id){
        $res = Db::name('article')->field("id,topic_id,topic_name,uid,cate_id,title,country,img,abst,info,md_info,add_time,updatetime,ordid,is_hot,is_index,status,wids,countrys,villages,areas,source,period")->where(['id'=>$id])->find();
        $res['img'] = empty($res['img'])?"":env('OSS.ALIURL').$res['img'];
        return $res;
    }


    /**
     * 更改酒闻状态
     *
     * @param array $param 更新参数
     * @return boolean true/false
     */
    public function changeArticleStatus($param){
        $result = Db::name('article')->save($param);
        (new ArticleService)->createJson($param['id']);
        if(isset($param['status'])){
            $param['status'] = is_numeric($param['status'])?intval($param['status']):'';
            if(!empty($param['status']) || $param['status'] === 0){//评论状态
                Db::name('article_comment')->where('aid','=',$param['id'])->update(['is_show'=>$param['status']]);
            }
        }
        return $result;
    }


    /**
     * 删除酒闻
     *
     * @param array $id 酒闻id
     * @return boolean true/false 返回结果
     */
    public function delArticle($id){
        // 启动事务
        $flag = 0;
        Db::startTrans();
        try {            
            Db::name('article')->where('id','in',$id)->delete();
            Db::name('honordy')->where('artid','in',$id)->delete();            
            // 提交事务
            Db::commit();
            $flag = 1;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
        }
        if($flag == 1){
            return true;
        }else{
            return false;
        }
    }


    /**
     * 排序
     *
     * @param array $param 入库参数
     * @return boolean 返回结果 true/false
     */
    public function articleSort($param){
        $model = new ArticleModel();
        return $model->saveAll($param);        
    }

    public function batchViewAdd($articleId)
    {
        return $this->whereIn('id',$articleId)->inc('viewnums')->update();
    }

    
}
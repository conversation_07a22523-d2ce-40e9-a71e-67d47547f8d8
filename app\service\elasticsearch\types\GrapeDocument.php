<?php
namespace app\service\elasticsearch\types;

class GrapeDocument extends Document
{
    protected $index = 'grape';
    protected $type = '_doc';
    protected $id;

    public function __construct($id)
    {
        $this->id = $id;
    }

    public function setHit($hit)
    {
        $this->hit = $hit;
    }

    public function setOperate($operate)
    {
        $this->operate = $operate;
    }
}
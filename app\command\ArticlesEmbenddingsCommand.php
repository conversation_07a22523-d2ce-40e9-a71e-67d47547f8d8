<?php


namespace app\command;


use Symfony\Component\VarDumper\Caster\RedisCaster;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;

class ArticlesEmbenddingsCommand extends Command
{

    protected function configure()
    {
        // 指令配置
        $this->setName('ArticlesEmbenddingsCommand')
            ->setDescription('the ArticlesEmbenddingsCommand command');
    }

    protected function execute(Input $input, Output $output)
    {

//        Db::name("article")->field('id,title,info')->where([['status','=',1],['cstatus','in',[0,1]]])->where('id','in',[8941,9258,6693,5876,5824,5110])->chunk(100,function ($res){
//            $pushData = [];
//            foreach ($res as $val){
//                $info = str_replace(array("\r\n", "\r", "\n"), "", $val['info']);
//                $info = strip_tags($info);
//                $info = str_replace("&nbsp;",'',$info);
//                $info = trim($info);
//                $text = "标题：".$val['title']."\n"."内容：".$info;
//                $data = array(
//                    "eid"=>$val['id'],
//                    "text"=>$text,
//                    "category"=>"wine_news",
//                );
//                $push = array(
//                    'exchange_name' => 'openai',
//                    'routing_key' => 'openai.embeddings',
//                    'data' =>base64_encode(json_encode($data))
//                );
//                #推送队列
//                $result = httpPostString(env('ITEM.QUEUE_URL'), json_encode($push));
//                echo $val['id']."请求结果：".json_encode($result)."\n";
//                unset($push);
//            }
//        });
//        echo "完成";exit;

        //推荐池老数据同步脚本
        $redis = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        $redis->auth(env('CACHE.PASSWORD'));
        $redis->select(7);
        $redis->del('vh_recommend_news1');
        $time  = time() - 86400 * 60;
        $where = [['updatetime', '>=', $time], ['status', '=', 1], ['img', '<>', '']];
        $list  = Db::name('article')->field("id,title,adminid,img,viewnums,status")->where($where)->order("id desc")->select()->toArray();
        foreach ($list as $val) {
            $data = [
                'id'         => (int)$val['id'],
                'title'      => $val['title'],
                'banner_img' => env('ALIURL') . $val['img'],
                'user_img'   => 'https://images.vinehoo.com/avatars/rabbit.png',
                'user_name'  => '酒云小编',
                'pageviews'  => (int)$val['viewnums']
            ];
            $res  = $redis->hSetNx('vh_recommend_news1', $val['id'], json_encode($data, JSON_UNESCAPED_UNICODE));
            echo $val['id'] . $res . "\n";
        }
        $oldKey = 'vh_recommend_news1';
        $newKey = 'vh_recommend_news';
        //获取旧键的所有字段
        $fields = $redis->hkeys($oldKey);
        $redis->del($newKey);
        //遍历字段并逐个重命名
        foreach ($fields as $field) {
            $value = $redis->hget($oldKey, $field);
            $redis->hset($newKey, $field, $value);
            $redis->hdel($oldKey, $field);
        }
        //删除旧键
        $redis->del($oldKey);
        echo 'ok';
        die;
    }
}
<?php
namespace app\model;

use think\facade\Db;
use think\Model;

/**
 * 酒闻资讯分类
 */
class ArticleTypeModel extends Model {
    protected $name='article_cate';
    protected $pagesize = 10;//默认分页数量
    protected $pagenumber = 1;//默认页码
    /**
     * 后台管理--酒闻类型列表
     *
     * @param array $param 查询条件参数
     * @return array $res 返回信息
     */
    public function getArticleTypeList($param,$fields){
        // if(isset($param['limit'])){
        //     $pagesize = $param['limit']?$param['limit']:$this->pagesize;//分页数量
        // }
        // if(isset($param['page'])){
        //     $pagenumber = $param['page']?$param['page']:$this->pagenumber;//页码
        // }
        // $startpagesize = $pagesize * ($pagenumber - 1);//起始位置

        
        $res = Db::name('article_cate')
        ->field($fields)
        ->where(['is_delete'=>0])
        ->select()->toArray();

        $totalNum = Db::name('article_cate')->where(['is_delete'=>0])->count();

        //$totalPage = (int)ceil($totalNum / $pagesize);
        $result['list'] = $res;
        // $result['totalPage'] = $totalPage;
        // $result['nowPage'] = $pagenumber;
        // $result['totalNum'] = $totalNum;
        // $result['limit'] = $pagesize;

        return $result;
    }


    /**
     * 后台管理--酒闻类型列表
     *
     * @param array $param 查询条件参数
     * @return array $res 返回信息
     */
    public function getArticleTypeDetails($param){
        
        $res = Db::name('article_cate')->field('id,name,enname,image,pid,status,is_best,sort_order,contactc')->where('id',$param['id'])->find();

        return $res;
    }
    

    /**
     * 后台管理--添加/编辑酒闻分类
     *
     * @param array $data 提交参数
     * @return boolean true/false 返回结果
     */
    public function operateArticleType($data){
        if(!isset($data['id']) || empty($data['id'])){//添加
            unset($data['id']);
            return Db::name('article_cate')->insert($data);
        }else{//编辑
            return Db::name('article_cate')->save($data);
        }
    }


    /**
     * 删除酒闻类型
     *
     * @param array $data 更新数据
     * @param array $id 更新id
     * @return boolean true/false 返回结果
     */
    public function delArticleType($data,$id){ 
        return Db::name('article_cate')->where('id','in',$id)->save($data);
    }
    

    /**
     * 排序
     *
     * @param array $param 入库参数
     * @return boolean 返回结果 true/false
     */
    public function articleTypeSort($param){
        /*$model = new ArticleTypeModel();
        return $model->saveAll($param);    */
        return Db::name('article_cate')->save($param);
    }
}
<?php
declare (strict_types=1);

namespace app\service\command;


use app\service\elasticsearch\Document;
use app\service\elasticsearch\types\CareasDocument;
use app\service\elasticsearch\types\WeiboDocument;
use app\service\ElasticSearchService;
use think\facade\Db;


class SynCareasToEsCommand
{
    private $document;
    private $model;

    private function init()
    {
        $this->document = new Document();
    }

    public function exec($model)
    {
        $this->init();
        $this->model = $model;

        $waitSynData = $this->model->where(['type' => 5])->select()->toArray();
        if(empty($waitSynData)){
//            exit;
        }
        $waitSynIdsArr = array_column($waitSynData, 'task_id');
        $reWaitSysData = array_column($waitSynData, null, 'task_id');

        $fields = [
            'cid',
            'carea_name',
            'carea_ename',
            'winenums',
//            'message',
            'catype',
        ];
        //$write_type 写入方式， 1单条写入 2批量写入
        $write_type = 2;
        for ($i=1;$i<=20;$i++) {


            $page  = $i;
            $limit = 5000;
            $data  = Db::name('careas')
                ->field($fields)
                ->limit(($page - 1) * $limit)
                ->where(['isdel' => 0])
                ->select()->toArray();

            foreach ($data as $key=>&$item) {
                $item['resource']          = 'careas';
                $item['effect_time_range'] = ['gte' => 0, 'lte' => 4747881840];
                $item['id']                = $item['cid'];
                $careasDocument             = new CareasDocument($item['id']);
                $operate                   = !empty($reWaitSysData[$item['id']]) ? $reWaitSysData[$item['id']]['operate'] : 'create';
//                $operate                   = $this->checkData($item['id'], $operate);
                if ($operate === true) {
                    $this->model->where(['task_id' => $item['id']])->delete();
                    continue;
                }
                $careasDocument->setIndex(env('ES.PREFIX') . 'careas_' . env('ES.ENV'));
                $careasDocument->setOperate($operate);
                $careasDocument->setHit($item);
                $careasDocument->toArray();
                $careasDocuments[] = $careasDocument;
                if($write_type == 1){ //单条写入
                    $result = $this->document->setDocument($careasDocument)->$operate();
                    $this->delData($result);
                }elseif($write_type == 2){ //批量写入
                    if( count($careasDocuments)  == 1000 ){
                        $result = $this->document->bulk($careasDocuments);
                        $careasDocuments = [];
                    }

                }
            }
        }
        if(!empty($careasDocuments)){
            $result = $this->document->bulk($careasDocuments);
        }
    }

    private function delData($result)
    {
        $this->model->where(['task_id' => $result['_id']])->delete();
    }

    public function checkData($id,$operate)
    {
        $searchService = new ElasticSearchService();
        $careas = 'careas';
        $input = [
            'index' => [$careas],
            'match' => [['id' => $id]]
        ];
        $res = $searchService->getDocumentList($input);

        if ($operate == 'update') {// update  delete 先判断数据是否存在，在决定是否入库
            if (empty($res['total']['value'])) {
                $operate = 'create';
            }

        }elseif($operate == 'create'){
            if (!empty($res['total']['value'])) {
                $operate = 'update';
            }

        } elseif ($operate == 'delete') {
            if (empty($res['total']['value'])) {
                return true;
            }
        }
        return $operate;
    }
}

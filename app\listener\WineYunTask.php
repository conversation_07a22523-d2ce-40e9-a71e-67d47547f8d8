<?php
declare (strict_types=1);

namespace app\listener;


use app\service\MicroService;
use think\facade\Cache;
use think\facade\Log;

class WineYunTask
{
    /**
     * 事件监听处理
     *
     * @return mixed
     */
    public function handle($event)
    {
        $prefix = 'platform_task_' . date('Y-m-d') . '_';

        $taskConfig = config('config')['task_condition'];//获取任务配置
        if (empty($taskConfig[$event['change_type']])) {
            return;
        }
        $condition = $taskConfig[$event['change_type']];//获取条件

        if (!empty($event['uid'])) {

            $key = $prefix . $event['change_type'] . '_' . $event['uid'];

            if ($event['change_type'] == 8) {//购买商品送兔头每买一次送一次
                $this->measureUpCondition(1, 1, $event, $key);
                return;
            }

            $currentNum = Cache::get($key);
            if (!$currentNum) {

                $timetoday = strtotime(date("Y-m-d", time()));//今天凌晨的过期时间戳

                $tomorrow = $timetoday + 3600 * 24;//计算第二天凌晨的过期时间戳

                $expire = $tomorrow - time();

                Cache::set($key, 1, $expire);

                $this->measureUpCondition(1, $condition, $event, $key);

            } else {
                if ($currentNum != -1) {
                    Cache::inc($key);
                    $currentNum = $currentNum + 1;
                    $this->measureUpCondition($currentNum, $condition, $event, $key);
                }
            }

        }
    }

    //判断是否完成条件
    private function measureUpCondition($currentNum, $condition, $event, $key)
    {
        $conditionNum = $condition['num'];

        if ($currentNum >= $conditionNum) {
            $microService = new MicroService();
            $params = ['tasks_type' => $event['change_type'], 'uuid' => '123', 'uid' => $event['uid']];
            $rewardResult = $microService->completeTaskByRabbitService($params, ['api-version' => 'v1']);

            if ($rewardResult == false) {
                $orderNo = !empty($event['order_no']) ? $event['order_no'] : '';
                Log::info(date('Y-m-d H:i:s') . '_增加兔头日志_{task_name}_{orderNo}_失败', ['order_no' => $orderNo, 'task_name' => $condition['task_name']]);
            }

            if ($rewardResult && $event['change_type'] != 8) {
                $timetoday = strtotime(date("Y-m-d", time()));//今天凌晨的过期时间戳

                $tomorrow = $timetoday + 3600 * 24;//计算第二天凌晨的过期时间戳
                $expire = $tomorrow - time();
                Cache::set($key, -1,$expire);//完成任务将key的值设置成-1
            }
        }
    }
}

<?php
namespace app\service\elasticsearch\types;

class ArticleDocument extends Document
{
    protected $index = 'article';
    protected $type = '_doc';
    protected $id;

    public function __construct($id)
    {
        $this->id = $id;
    }

    public function setHit($hit)
    {
        $this->hit = $hit;
    }

    public function setOperate($operate)
    {
        $this->operate = $operate;
    }
}
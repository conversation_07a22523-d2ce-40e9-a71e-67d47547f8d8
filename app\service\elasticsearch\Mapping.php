<?php
namespace app\service\elasticsearch;

use Elasticsearch\ClientBuilder;

/**
 * 创建es mapping
 * Class MappingService
 * @package App\Services\Elasticsearch
 */

class Mapping
{
    /**
     * 创建mapping
     * @param array $data
     * @return array
     */
    public function create($data)
    {

        $client = Client::getInstance();
        $result = $client->index($data);

        return $result;
    }
}
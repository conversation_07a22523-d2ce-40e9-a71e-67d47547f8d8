<?php

namespace app\service\admin;

use app\BaseService;
use app\model\ArticleTypeModel;
use Config;

use think\facade\Validate;
use function GuzzleHttp\json_decode;

/**
 * 后台管理--酒闻资讯分类
 */
class ArticleTypeService extends BaseService {
    /**
     * 后台管理--酒闻列表
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function getArticleTypeList($param,$header){    
        
        //查询数据
        $ArticleTypeModel = new ArticleTypeModel();
        $fields = 'id,name,create_time';
        $data = $ArticleTypeModel->getArticleTypeList($param,$fields);

        //数据处理
        $arr = [];
        $cate = [];
        if(!empty($data['list'])){   
            $list = array_column($data['list'],null,'id');  
            foreach($data['list'] as $key => $val){
                //相关分类处理
//                $contactc = explode(',',$val['contactc']);
//                foreach($contactc as $ckey => $cval){
//                    $cate[$key][] = isset($list[$cval]['name'])?$list[$cval]['name']:'';
//                }
//
//                $val['contactc'] = implode(',',$cate[$key]);
                
                $arr[] = $val;
            }
            
        }

        $data['list'] = $arr;

        return  $data;
    }


    /**
     * 后台管理--酒闻分类查询
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function getArticleType($param,$header){    
        
        //查询数据
        $ArticleTypeModel = new ArticleTypeModel();
        $fields = 'id,name,create_time';
        $data = $ArticleTypeModel->getArticleTypeList($param,$fields);
       
        return  $data;
    }


    /**
     * 后台管理--酒闻分类详情查询
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function getArticleTypeDetails($param,$header){    
        
        //查询数据
        $ArticleTypeModel = new ArticleTypeModel();
        $data = $ArticleTypeModel->getArticleTypeDetails($param);
       
        return  $data;
    }
     

    /**
     * 后台管理--添加/编辑酒闻分类
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function operateArticleType($param,$header){ 
        if(empty($param['name'])){
            $this->throwError('分类名称不能为空');
        }

        $ArticleTypeModel = new ArticleTypeModel();

        $save = array(
            'pid' => isset($param['pid'])?$param['pid']:'',//上级分类
            'image' => isset($param['image'])?$param['image']:'',//分类图标
            'name' => isset($param['name'])?$param['name']:'',//分类名称
            'sort_order' => isset($param['sort_order'])?$param['sort_order']:'',//排序
            'status' => isset($param['status'])?$param['status']:'',//审核( 0未审核 1已审核)
            'is_best' => isset($param['is_best'])?$param['is_best']:'',//推荐(1是 0否)
            'type' => isset($param['type'])?$param['type']:0,//发布帖子（1允许 0不允许）
            'contactc' => isset($param['contactc'])?$param['contactc']:'',//相关分类
            'id' => isset($param['id'])?$param['id']:'',//分类id
            'create_time'=> date('Y-m-d H:i:s',time()),
        );
        $is_res = $ArticleTypeModel->operateArticleType($save);
        if($is_res === false){
            $res['flag'] = 0;
            $res['msg'] = '操作失败';
        }else{
            $res['flag'] = 1;
            $res['msg'] = '操作成功';
        }

        return $res;
    }  

    
    /**
     * 更改酒闻类型状态
     *
     * @param array $param 提交参数
     * @param array $header 头部参
     * @return array $res 返回结果
     */
    public function changeArticleTypeStatus($param,$header){
        if(empty($param['type'])){
            $this->throwError('类型不能为空');
        }

        if($param['type'] == 1){//推荐
            $save['is_best'] = $param['status'];
        }
        $save['id'] = $param['id'];//酒闻类型id

        $ArticleTypeModel = new ArticleTypeModel();        

        $is_res = $ArticleTypeModel->operateArticleType($save);
        if($is_res){
            $res['flag'] = 1;
            $res['msg'] = '操作成功';
        }else{
            $res['flag'] = 0;
            $res['msg'] = '操作失败';
        }

        return $res;
    }


    /**
     * 删除酒闻类型
     *
     * @param array $param 提交参数
     * @param array $header 头部参
     * @return array $res 返回结果
     */
    public function delArticleType($param,$header){        
        if(!isset($param['id']) || empty($param['id'])){
            $this->throwError('酒闻类型id不能为空');
        }

        $ArticleTypeModel = new ArticleTypeModel();

        $where = explode(',',$param['id']);//更新条件
        
        $save['is_delete'] = 1;//更新数据

        $is_res = $ArticleTypeModel->delArticleType($save,$where);
        if($is_res){
            $res['flag'] = 1;
            $res['msg'] = '操作成功';
        }else{
            $res['flag'] = 0;
            $res['msg'] = '操作失败';
        }

        return $res;
    }

    
    /**
     * 酒闻类型排序
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function articleTypeSort($param,$header){
        $validate = Validate::rule('id|酒闻类型', 'require|number')
            ->rule([
                'sort_order|排序值'  => 'require|number',
            ]);

        if (!$validate->check($param)) {
            $this->throwError($validate->getError());
        }

        $save['id'] = $param['id'];
        $save['sort_order'] = $param['sort_order'];
        //数据入库
        $ArticleTypeModel = new ArticleTypeModel();
        $is_res = $ArticleTypeModel->articleTypeSort($save);

        if($is_res === false){
            $msg['flag'] = 0;
            $msg['msg'] = '操作失败';
        }else{
            $msg['flag'] = 1;
            $msg['msg'] = '操作成功';
        }
        return $msg;
    }
}
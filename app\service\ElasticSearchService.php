<?php
namespace app\service;


use app\BaseService;
use app\service\elasticsearch\Document;
use app\service\elasticsearch\Search;
use app\service\elasticsearch\searches\BoolSearch;
use app\service\elasticsearch\searches\FullSearch;

/**
 * Class ElasticSearchService
 * @package App\Services
 */

class ElasticSearchService extends BaseService
{
    private $document;
    public function setDocument(Document $document)
    {
        $this->document = $document;
    }
    /**
     * 新建文档
     * @param $data
     * @return array
     */
    public function createDocument($data)
    {
        $result = $this->document->create($data);

        return $result;
    }

    /**
     * 更新文档
     * @param $id
     * @param $data
     */
    public function updateDocument($id, $data)
    {
        $result = $this->document->update($id, $data);

        return $result;
    }

    /**
     * 获取文档列表
     * @param $data
     */
    public function getDocumentList($input, $type = "bool")
    {
        $response = $this->search($input, $type);

        return $response;// 不影响调用方业务，不抛异常
    }

    /**
     * 批量操作 新增 修改 删除
     * @param array $documents
     * @return array
     */
    public function bulk(array $documents)
    {
        $document = new Document();

        $response = $document->bulk($documents);

        return $response;
    }

    /**
     * 查询
     * @param array $input
     * @param string $type
     * @return array|null
     */
    public function search($input = [], $type = 'bool')
    {
        $response = null;
        switch ($type) {
            case "other":break;
            case "full":
                $response = $this->fullSearchDocument($input);
                break;
            default:
                $response = $this->synthesizeBoolSearchDocument($input);
        }
        return $response;
    }
    /**
     * bool 查询
     * @param array $input
     * @return array
     */
    private function synthesizeBoolSearchDocument($input = [])
    {
        $booleSearch = new BoolSearch();
        $booleSearch->setInput($input);

        $search = new Search();
        $response = $search->setSearch($booleSearch)->doSearch()->toArray();
        return $response;
    }

    /**
     * 全文 查询
     * @param array $input
     * @return $this|array
     */
    public function fullSearchDocument(array $input)
    {
        if (empty($input) or !is_array($input)) {
            return [];
        }
        if (empty($input['index'])) {
            $this->throwError('参数错误');
        }

        $fullSearch = new FullSearch();
        $fullSearch->setInput($input);
        $search = new Search();
        $response = $search->setSearch($fullSearch)->doSearch()->toArray();
        return $response;
    }
}
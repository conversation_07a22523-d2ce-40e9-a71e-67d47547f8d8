<?php
declare (strict_types=1);

namespace app\service\command;


use app\model\WeiboModel;
use app\service\elasticsearch\Document;
use app\service\elasticsearch\types\GrapeDocument;
use app\service\elasticsearch\types\WeiboDocument;
use app\service\ElasticSearchService;
use think\facade\Db;


class SynGrapeToEsCommand
{
    private $document;
    private $model;

    private function init()
    {
        $this->document = new Document();
    }

    public function exec($model)
    {
        $this->init();
        $this->model = $model;

        $waitSynData = $this->model->where(['type' => 6])->select()->toArray();
        if(empty($waitSynData)){
//            exit;
        }
        $waitSynIdsArr = array_column($waitSynData, 'task_id');
        $reWaitSysData = array_column($waitSynData, null, 'task_id');

        $fields = [
            'gid',
            'cnname',
            'enname',
            'image',
            'bnname',
            'message',
            'is_hot',
        ];
        //$write_type 写入方式， 1单条写入 2批量写入
        $write_type = 2;
        for ($i=1;$i<=20;$i++) {
            $page  = $i;
            $limit = 5000;
            $data = Db::name('grape')
                ->field($fields)
                ->limit(($page-1)*$limit,$limit)
                ->select()->toArray();
            foreach ($data as &$item) {
                $item['resource'] = 'grape';
                $item['effect_time_range'] = ['gte' => 0, 'lte' => 4747881840];
                $item['id'] = $item['gid'];
                $grapeDocument = new GrapeDocument($item['id']);
                $operate = !empty($reWaitSysData[$item['id']]) ? $reWaitSysData[$item['id']]['operate'] : 'create';
        //            $operate = $this->checkData($item['id'],$operate);
                if($operate === true){
                    $this->model->where(['task_id' => $item['id']])->delete();
                    continue;
                }
                $grapeDocument->setIndex(env('ES.PREFIX').'grape_'.env('ES.ENV'));
                $grapeDocument->setOperate($operate);
                $grapeDocument->setHit($item);
                $grapeDocument->toArray();

                $grapeDocuments[] = $grapeDocument;
                if($write_type == 1){ //单条写入
                    $result = $this->document->setDocument($grapeDocument)->$operate();
//                    $this->delData($result);
                }elseif($write_type == 2){ //批量写入

                }
            }
        }
        $result = $this->document->bulk($grapeDocuments);
    }

    private function delData($result)
    {
        $this->model->where(['task_id' => $result['_id']])->delete();
    }

    public function checkData($id,$operate)
    {
        $searchService = new ElasticSearchService();
        $grape = 'grape';
        $input = [
            'index' => [$grape],
            'match' => [['id' => $id]]
        ];
        $res = $searchService->getDocumentList($input);

        if ($operate == 'update') {// update  delete 先判断数据是否存在，在决定是否入库
            if (empty($res['total']['value'])) {
                $operate = 'create';
            }

        }elseif($operate == 'create'){
            if (!empty($res['total']['value'])) {
                $operate = 'update';
            }

        } elseif ($operate == 'delete') {
            if (empty($res['total']['value'])) {
                return true;
            }
        }
        return $operate;
    }
}

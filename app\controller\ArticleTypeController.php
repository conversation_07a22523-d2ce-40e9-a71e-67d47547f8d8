<?php
namespace app\controller;

use app\BaseController;
use app\Request;
use app\service\admin\ArticleTypeService;

/**
 * 后台管理--酒闻分类管理
 */
class ArticleTypeController extends BaseController
{
    /**
    * @OA\Post(path="/lunjiu/admin/articletype/getArticleTypeList",
    *   tags={"酒闻类型后台管理"},
    *   summary="酒闻类型列表",
    *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
    *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
    *   @OA\RequestBody(
    *     @OA\MediaType(
    *       mediaType="multipart/form-data",
    *         @OA\Schema(
    *                       
    *           )
    *       )
    *     ), 
    *    @OA\Response(
    *            response=200,
    *            description="接口请求成功",
    *            @OA\MediaType(
    *                mediaType="application/json",
    *            @OA\Schema(
    *                 @OA\Property(property="status",
    *                    type="string",
    *                    example="success/fail",
    *                    description="状态"
    *                ),
    *                 @OA\Property(property="errorCode",
    *                    type="string",
    *                    example="0/-1",
    *                    description="状态编码"
    *                ),
    *                 @OA\Property(property="msg",
    *                    type="string",
    *                    example="ok",
    *                    description="状态描述"
    *                ),
    *                @OA\Property(property="data",
    *                   type="object",description="数据",
    *                   @OA\Property(property="list",type="array",description="酒闻分类数据",
    *                       @OA\Items(
    *                           type="object",description="",
    *                           @OA\Property(property="id",type="integer",example="1",description="分类id"),
    *                           @OA\Property(property="name",type="string",example="毛毛虫化蝶传记",description="分类中文名称"),
    *                           @OA\Property(property="image",type="string",example="/image.jpg",description="分类图标"),
    *                           @OA\Property(property="sort_order",type="integer",example="排序",description="88"),
    *                           @OA\Property(property="contactc",type="string",example="10",description="相关分类"),
    *                           @OA\Property(property="is_best",type="integer",example="1",description="是否推荐 1是 0否"),
    *                       )
    *                   ),
    *               ),
    *            )
    *        ),
    *    )
    * )
    */
    public function getArticleTypeList(Request $request){
        $param = $request->param();
        $header = $request->header();
        $is_true = isset($header['securitycheckval']);//判断是否存在token参数
        $header['securitycheckval'] = $is_true?$header['securitycheckval']:'';//token值
        $ArticleTypeService = new ArticleTypeService();
        $is_res = $ArticleTypeService->getArticleTypeList($param,$header);     
        
        return $this->success($is_res);
    }


    /**
    * @OA\Post(path="/lunjiu/admin/articletype/getArticleType",
    *   tags={"酒闻类型后台管理"},
    *   summary="酒闻类型",
    *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
    *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
    *   @OA\RequestBody(
    *     @OA\MediaType(
    *       mediaType="multipart/form-data",
    *         @OA\Schema(
    *                       
    *           )
    *       )
    *     ), 
    *    @OA\Response(
    *            response=200,
    *            description="接口请求成功",
    *            @OA\MediaType(
    *                mediaType="application/json",
    *            @OA\Schema(
    *                 @OA\Property(property="status",
    *                    type="string",
    *                    example="success/fail",
    *                    description="状态"
    *                ),
    *                 @OA\Property(property="errorCode",
    *                    type="string",
    *                    example="0/-1",
    *                    description="状态编码"
    *                ),
    *                 @OA\Property(property="msg",
    *                    type="string",
    *                    example="ok",
    *                    description="状态描述"
    *                ),
    *                @OA\Property(property="data",
    *                   type="object",description="数据",
    *                   @OA\Property(property="list",type="array",description="酒闻分类数据",
    *                       @OA\Items(
    *                           type="object",description="",
    *                           @OA\Property(property="id",type="integer",example="1",description="分类id"),
    *                           @OA\Property(property="name",type="string",example="毛毛虫化蝶传记",description="分类中文名称"),
    *                       )
    *                   ),
    *               ),
    *            )
    *        ),
    *    )
    * )
    */
    public function getArticleType(Request $request){
        $param = $request->param();
        $header = $request->header();
        $is_true = isset($header['securitycheckval']);//判断是否存在token参数
        $header['securitycheckval'] = $is_true?$header['securitycheckval']:'';//token值
        $ArticleTypeService = new ArticleTypeService();
        $is_res = $ArticleTypeService->getArticleType($param,$header);     
        
        return $this->success($is_res);
    }


    /**
    * @OA\Post(path="/lunjiu/admin/articletype/getArticleTypeDetails",
    *   tags={"酒闻类型后台管理"},
    *   summary="酒闻类型详情",
    *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
    *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
    *   @OA\RequestBody(
    *     @OA\MediaType(
    *       mediaType="multipart/form-data",
    *         @OA\Schema(
    *               @OA\Property(description="酒闻分类id", property="id", type="integer", default=""),                       
    *           )
    *       )
    *     ), 
    *    @OA\Response(
    *            response=200,
    *            description="接口请求成功",
    *            @OA\MediaType(
    *                mediaType="application/json",
    *            @OA\Schema(
    *                 @OA\Property(property="status",
    *                    type="string",
    *                    example="success/fail",
    *                    description="状态"
    *                ),
    *                 @OA\Property(property="errorCode",
    *                    type="string",
    *                    example="0/-1",
    *                    description="状态编码"
    *                ),
    *                 @OA\Property(property="msg",
    *                    type="string",
    *                    example="ok",
    *                    description="状态描述"
    *                ),
    *                @OA\Property(property="data",
    *                   type="object",description="数据",
    *                   @OA\Property(property="list",type="array",description="酒闻分类数据",
    *                       @OA\Items(
    *                           type="object",description="",
    *                           @OA\Property(property="id",type="integer",example="1",description="分类id"),
    *                           @OA\Property(property="name",type="string",example="毛毛虫化蝶传记",description="分类中文名称"),
    *                           @OA\Property(property="enname",type="string",example="english",description="分类英文名称"),
    *                           @OA\Property(property="image",type="string",example="/image.jpg",description="分类图标"),
    *                           @OA\Property(property="pid",type="integer",example="1",description="上级分类id"),
    *                           @OA\Property(property="status",type="integer",example="1",description="是否审核 0未审核 1已审核"),
    *                           @OA\Property(property="is_best",type="integer",example="1",description="是否推荐 1是 0否"),
    *                           @OA\Property(property="sort_order",type="integer",example="排序",description="88"),
    *                           @OA\Property(property="contactc",type="string",example="10",description="相关分类"),   
    *                       )
    *                   ),
    *               ),
    *            )
    *        ),
    *    )
    * )
    */
    public function getArticleTypeDetails(Request $request){
        $param = $request->param();
        $header = $request->header();
        $is_true = isset($header['securitycheckval']);//判断是否存在token参数
        $header['securitycheckval'] = $is_true?$header['securitycheckval']:'';//token值
        $ArticleTypeService = new ArticleTypeService();
        $is_res = $ArticleTypeService->getArticleTypeDetails($param,$header);     
        
        return $this->success($is_res);
    }


    /**
    * @OA\Post(path="/lunjiu/admin/articletype/operateArticleType",
    *   tags={"酒闻类型后台管理"},
    *   summary="添加/编辑酒闻类型",
    *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
    *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
    *   @OA\RequestBody(
    *     @OA\MediaType(
    *       mediaType="multipart/form-data",
    *         @OA\Schema(
    *               @OA\Property(description="上级分类", property="pid", type="string", default=""),
    *               @OA\Property(description="分类图标", property="image", type="string", default=""),
    *               @OA\Property(description="分类名称", property="name", type="string", default=""),
    *               @OA\Property(description="排序", property="sort_order", type="integer", default=""),
    *               @OA\Property(description="审核( 0未审核 1已审核)", property="status", type="integer", default=""),    
    *               @OA\Property(description="推荐(1是 0否)", property="is_best", type="integer", default="10"),
    *               @OA\Property(description="相关分类", property="contactc", type="string", default="1,2,4"),     
    *               @OA\Property(description="分类id（编辑必传）", property="id", type="integer", default=""),
    *           required={"name"})
    *       )
    *     ), 
    *    @OA\Response(
    *            response=200,
    *            description="接口请求成功",
    *            @OA\MediaType(
    *                mediaType="application/json",
    *            @OA\Schema(
    *                 @OA\Property(property="status",
    *                    type="string",
    *                    example="success/fail",
    *                    description="状态"
    *                ),
    *                 @OA\Property(property="errorCode",
    *                    type="string",
    *                    example="0/-1",
    *                    description="状态编码"
    *                ),
    *                 @OA\Property(property="msg",
    *                    type="string",
    *                    example="ok",
    *                    description="状态描述"
    *                ),
    *                @OA\Property(property="data",
    *                   type="object",description="数据",
    *               ),
    *            )
    *        ),
    *    )
    * )
    */
    public function operateArticleType(Request $request){
        $param = $request->param();
        $header = $request->header();
        $is_true = isset($header['securitycheckval']);//判断是否存在token参数
        $header['securitycheckval'] = $is_true?$header['securitycheckval']:'';//token值

        validate(\app\validate\ArticleTypeValidate::class)->check($param);

        $ArticleTypeService = new ArticleTypeService();
        $is_res = $ArticleTypeService->operateArticleType($param,$header);     
        
        if($is_res['flag'] == 1){
            return $this->success($is_res['msg']);
        }else{
            return $this->failed($is_res['msg'],$is_res['flag']);
        }
    }


    /**
    * @OA\Post(path="/lunjiu/admin/articletype/changeArticleTypeStatus",
    *   tags={"酒闻类型后台管理"},
    *   summary="更改酒闻类型状态",
    *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
    *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
    *   @OA\RequestBody(
    *     @OA\MediaType(
    *       mediaType="multipart/form-data",
    *         @OA\Schema(
    *               @OA\Property(description="酒闻类型id", property="id", type="integer", default=""),    
    *               @OA\Property(description="类型 1推荐", property="type", type="integer", default=""),
    *               @OA\Property(description="状态 1：是 0：否", property="status", type="integer", default=""),
    *           required={"id","type","status"})
    *       )
    *     ), 
    *    @OA\Response(
    *            response=200,
    *            description="接口请求成功",
    *            @OA\MediaType(
    *                mediaType="application/json",
    *            @OA\Schema(
    *                 @OA\Property(property="status",
    *                    type="string",
    *                    example="success/fail",
    *                    description="状态"
    *                ),
    *                 @OA\Property(property="errorCode",
    *                    type="string",
    *                    example="0/-1",
    *                    description="状态编码"
    *                ),
    *                 @OA\Property(property="msg",
    *                    type="string",
    *                    example="ok",
    *                    description="状态描述"
    *                ),
    *                @OA\Property(property="data",
    *                   type="object",description="数据",
    *               ),
    *            )
    *        ),
    *    )
    * )
    */
    public function changeArticleTypeStatus(Request $request){
        $param = $request->param();
        $header = $request->header();
        $is_true = isset($header['securitycheckval']);//判断是否存在token参数
        $header['securitycheckval'] = $is_true?$header['securitycheckval']:'';//token值
        $ArticleTypeService = new ArticleTypeService();
        $is_res = $ArticleTypeService->changeArticleTypeStatus($param,$header);  
        
        if($is_res['flag'] == 1){
            return $this->success($is_res['msg']);
        }else{
            return $this->failed($is_res['msg'],$is_res['flag']);
        }
    }


    /**
    * @OA\Post(path="/lunjiu/admin/articletype/delArticleType",
    *   tags={"酒闻类型后台管理"},
    *   summary="删除酒闻类型",
    *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
    *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
    *   @OA\RequestBody(
    *     @OA\MediaType(
    *       mediaType="multipart/form-data",
    *         @OA\Schema(
    *               @OA\Property(description="酒闻类型id（批量删除是id英文逗号分隔的字符串）", property="id", type="string", default=""),
    *           required={"id"})
    *       )
    *     ), 
    *    @OA\Response(
    *            response=200,
    *            description="接口请求成功",
    *            @OA\MediaType(
    *                mediaType="application/json",
    *            @OA\Schema(
    *                 @OA\Property(property="status",
    *                    type="string",
    *                    example="success/fail",
    *                    description="状态"
    *                ),
    *                 @OA\Property(property="errorCode",
    *                    type="string",
    *                    example="0/-1",
    *                    description="状态编码"
    *                ),
    *                 @OA\Property(property="msg",
    *                    type="string",
    *                    example="ok",
    *                    description="状态描述"
    *                ),
    *                @OA\Property(property="data",
    *                   type="object",description="数据",
    *               ),
    *            )
    *        ),
    *    )
    * )
    */
    public function delArticleType(Request $request){
        $param = $request->param();
        $header = $request->header();
        $is_true = isset($header['securitycheckval']);//判断是否存在token参数
        $header['securitycheckval'] = $is_true?$header['securitycheckval']:'';//token值
        $ArticleTypeService = new ArticleTypeService();
        $is_res = $ArticleTypeService->delArticleType($param,$header); 
        
        if($is_res['flag'] == 1){
            return $this->success($is_res['msg']);
        }else{
            return $this->failed($is_res['msg'],$is_res['flag']);
        }
    }


    /**
    * @OA\Post(path="/lunjiu/admin/articletype/articleTypeSort",
    *   tags={"酒闻类型后台管理"},
    *   summary="排序",
    *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
    *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
    *   @OA\RequestBody(
    *       @OA\MediaType(
    *           mediaType="multipart/form-data",
     *         @OA\Schema(
     *              @OA\Property(property="id",type="integer",example="1",description="酒闻类型id"),
     *              @OA\Property(property="sort_order",type="integer",example="1",description="排序"),
     *           required={"id","sort_order"})
     *       )
     *     ),
    *   @OA\Response(
    *         response=200,
    *         description="接口请求成功",
    *         @OA\MediaType(
    *         mediaType="application/json",
    *         @OA\Schema(
    *             @OA\Property(property="status",
    *                 type="string",
    *                 example="ok/fail",
    *                 description="状态"
    *             ),
    *             @OA\Property(property="errorCode",
    *                 type="string",
    *                 example="0/-1",
    *                 description="状态编码"
    *             ),
    *             @OA\Property(property="msg",
    *                 type="string",
    *                 example="ok",
    *                 description="状态描述"
    *             ),
    *             @OA\Property(property="data",
    *             type="object",description="返回参数",
    
    *                   ),
    *               ),
    *            )
    *         ),
    *     )
    * )
    */    
    public function articleTypeSort(Request $request){        
        $param = $request->param();
        $header = $request->header();
        $is_true = isset($header['securitycheckval']);//判断是否存在token参数
        $header['securitycheckval'] = $is_true?$header['securitycheckval']:'';//token值
        $ArticleTypeService = new ArticleTypeService();
        $is_res = $ArticleTypeService->articleTypeSort($param,$header); 

        if($is_res['flag'] == 1){
            return $this->success($is_res['msg']);
        }else{
            return $this->failed($is_res['msg'],$is_res['flag']);
        }
    }

}
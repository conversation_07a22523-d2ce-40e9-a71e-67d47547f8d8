<?php
namespace app\model;

use think\facade\Db;
use think\Model;

/**
 * 酒闻评论
 */
class ArticleCommentModel extends Model {
    protected $name='article_comment';
    protected $pagesize = 10;//默认分页数量
    protected $pagenumber = 1;//默认页码

    /**
     * 后台管理--酒闻评论列表
     *
     * @param array $param 查询条件参数
     * @return array $res 返回信息
     */
    public function getArticleCommentList($param){
        if(isset($param['limit'])){
            $pagesize = $param['limit']?$param['limit']:$this->pagesize;//分页数量
        }
        if(isset($param['page'])){
            $pagenumber = $param['page']?$param['page']:$this->pagenumber;//页码
        }

        $startpagesize = $pagesize * ($pagenumber - 1);//起始位置

        //查询条件处理
        $where = [];
        if(!empty($param['keywords'])){//评论关键字
            $where[] = ['ac.content','like', "%{$param['keywords']}%"];
        }   

        if(!empty($param['aid'])){//资讯id
            $where[] = ['ac.aid','=', $param['aid']];
        }

        if(!empty($param['uid'])){ //用户id
            $where[] = ['ac.uid','=',$param['uid']];
        }

        if(!empty($param['recomid'])){ //评论id
            $where[] = ['ac.recomid','=',$param['recomid']];
        }

        if(isset($param['is_show'])){
            $param['is_show'] = is_numeric($param['is_show'])?intval($param['is_show']):'';
            if(!empty($param['is_show']) || $param['is_show'] === 0){//评论状态
                $where[] = ['ac.is_show','=',$param['is_show']];
            }
        }

        if(isset($param['audit_status']) &&$param['audit_status'] !=""){
            $param['audit_status'] = is_numeric($param['audit_status'])?intval($param['audit_status']):'';
            if(!empty($param['audit_status']) || $param['audit_status'] === 0){//评论审核状态
                $where[] = ['ac.audit_status','=',$param['audit_status']];
            }
        }else{
            //非待审核列表
            $where[] = ['ac.audit_status','<>',1];
        }


        $res = Db::name('article_comment')->alias('ac')
        ->field('a.title,ac.comid,ac.recomid as pid,ac.uid,ac.aid,ac.content,ac.is_show,ac.addtime,ac.audit_status,ac.diggnums,ac.hot_vaule,emoji_image')
        ->join('article a','a.id = ac.aid','left')
        ->where($where)
        ->order(['ac.addtime'=>'desc'])//根据显示状态和更新时间显示 'ac.is_show'=>'desc',
        ->limit($startpagesize,$pagesize)->select()->toArray();
        $totalNum = Db::name('article_comment')->alias('ac')->join('article a','a.id = ac.aid','left')->where($where)->count();

        $totalPage = (int)ceil($totalNum / $pagesize);
        $result['list'] = $res;
        //$result['totalPage'] = $totalPage;;
        $result['total'] = $totalNum;

        return $result;
    }


    /**
     * 后台管理--删除酒闻评论
     *
     * @param array $id 更新id
     * @return boolean true/false 返回结果
     */
    public function delArticleComment($id,$is_show){
        return Db::name('article_comment')->where('comid','in',$id)->save(['is_show'=>$is_show]);
    }

    
}
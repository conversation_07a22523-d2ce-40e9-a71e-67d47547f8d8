{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.1.0", "topthink/framework": "^6.0.0", "topthink/think-orm": "^2.0", "guzzlehttp/guzzle": "~6.0", "old-smoke-gun/php-jwt": "^1.0", "aliyuncs/oss-sdk-php": "^2.6", "alibabacloud/cdn-20180510": "^1.0", "elasticsearch/elasticsearch": "7.14"}, "require-dev": {"symfony/var-dumper": "^4.2", "topthink/think-trace": "^1.0"}, "autoload": {"psr-4": {"app\\": "app"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist"}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}}
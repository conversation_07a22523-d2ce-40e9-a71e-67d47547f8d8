<?php

namespace app;
use app\util\JwtUtil;
use think\cache\driver\Redis;
use think\Validate;

abstract class BaseService
{
  use ApiResponse;

    public function httpRequest(string $url, array $params = [], string $method = 'POST', array $configs = [], string $contentType = 'form_params')
    {
        $configs['timeout'] = $configs['timeout'] ?? 5;
        $client = new \GuzzleHttp\Client($configs);
        $params = strtoupper($method) == 'GET' ? ['query' => $params] : [$contentType => $params];

        try {
            $request = $client->request($method, $url, $params);
        } catch (\app\exception\RequestException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
            $data = [
                'code' =>$code,
                'errorCode'=>'-1',
                'msg'=>$message,
                'data'=>(object)[]
            ];
            throw new \app\exception\RequestException($data);
        }

        $return = $request->getBody()->getContents();
        $response = json_decode($return, true);

        return $response;
    }

    public function httpGet(string $url, array $params = [], array $configs = [])
    {
        $configs['timeout'] = $configs['timeout'] ?? 5;
        $client = new \GuzzleHttp\Client($configs);
        $params = ['query' => $params];

        try {
            $request = $client->request('GET', $url, $params);
        } catch (\app\exception\RequestException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
            $data = [
                'code' =>$code,
                'errorCode'=>'-1',
                'msg'=>$message,
                'data'=>(object)[]
            ];
            throw new \app\exception\RequestException($data);
        }
        $return = $request->getBody()->getContents();
        $response = json_decode($return, true);
        return $response;
    }

    public function httpPost(string $url, array $params = [], array $configs = [], string $contentType = 'form_params')
    {
        $configs['timeout'] = $configs['timeout'] ?? 5;
        $client = new \GuzzleHttp\Client($configs);
        $params = [$contentType => $params];

        try {
            $request = $client->request('POST', $url, $params);
        } catch (\app\exception\RequestException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
            $data = [
                'code' =>$code,
                'errorCode'=>'999',
                'msg'=>$message,
                'data'=>(object)[]
            ];
            throw new \app\exception\RequestException($data);
        }

        $return = $request->getBody()->getContents();
        $response = json_decode($return, true);
        return $response;
    }


    /**
     * 校验Redis存储的token是否过期
     * @param string $tokenval token值
     * @return array $msg 返回结果
     */
    public function checkRedisToken($tokenval){
        if(empty($tokenval) || $tokenval == ''){
            $msg['flag'] = 4;
            $msg['msg'] = '请先登陆';
            $this->throwError($msg['msg'],$msg['flag']);
        }


        $data = $this->checkJwtToken($tokenval);//jwt验证token是否有效

        $Redis = new Redis(Config('cache')['stores']['redis']);
        $userinfo = $this->analysisToken($tokenval);//解析token
        $tokenname = 'securitycheckval'.'_'.$userinfo['uid'].'_'.$userinfo['froms'];//token名称

        $is_token = $Redis->get( $tokenname);//获取redis
        //判断传入的token值与Redis中的是否相等
        if($tokenval != $is_token){
            $msg['flag'] = 777;
            $msg['msg'] = '你被挤下线了';
            $this->throwError($msg['msg'], $msg['flag'], 401);
        }

        $msg['flag'] = 1;
        $msg['msg'] = '安全验证通过';
        $msg['data'] = $userinfo;
        if($data['flag'] == 1){
            //验证redis是否过期
            $isinfo = $Redis->has($tokenname);
            if($isinfo == false){
                $msg['flag'] = 777;
                $msg['msg'] = 'token已过期';
                $this->throwError($msg['msg'], $msg['flag'], 401);
            }         
        }else{
            $msg['flag'] = $data['flag'];
            $msg['msg'] = $data['msg'];
            $this->throwError($msg['msg'], $msg['flag'], 401);
        }

        return $msg;

    }


    /**
     * token 校验
     * @param string $tokenval token值
     * @return array $dta 返回值
     */
    public function checkJwtToken($tokenval){
        //jwt验证token
        $jwt = new \oldSmokeGun\Jwt\Jwt();

        $tokenkey = Config('config')['TOKEN_KEY'];//秘钥
        $result = $jwt->setSecret($tokenkey)
            ->validate($tokenval);
        switch ( $result )
        {
            case 0 :
                $data['flag'] = 1;
                $data['msg'] = 'token 有效';
                break;
            case 1 :
                $data['flag'] = 777;
                $data['msg'] = '签名验证错误';
                break;
            case 2 :
                $data['flag'] = 777;
                $data['msg'] = 'token 不可用';
                break;
            case 3 :
                $data['flag'] = 777;
                $data['msg'] = 'token 已过期';
                break;
        }
        
        return $data;
    }

    /**
     * token 解析
     * @param string $tokenval token值
     * @return array $dta 返回值
     */
    public function analysisToken($tokenval){
        //$jwt = new \oldSmokeGun\Jwt\Jwt();
        //$data = $jwt->parse($tokenval);
        //$userinfo = $data['payload']['extra'];//用户基本信息
        $userinfo['id'] = 1;
        $userinfo['username'] = '测试人员';
        return $userinfo;
    }


    /**
     * 内部模块之间请求接口
     *
     * @param array $header 头部参数
     * @param array $body 提交参数
     * @param string $url 请求地址
     * @param int $service 所属微服务 1：user 2：mall 3：coupon 4：lunjiu 5：upload
     * @param int $request_type 结果请求类型 1=post 2=get
     * @return array $info 返回结果
     */
    public function PubForward($header,$body,$url,$service,$request_type=1){
        $commonHttp = new \app\CommonHttpRequest();
        //请求地址 
        if($service == 1){//用户
            $serviceurl = env('ITEM.USER_URL');
        }else if($service == 2){//商城
            $serviceurl = env('CONFIG.REQUEST_MALL_URL');

        }else if($service == 3){//优惠券
            $serviceurl = env('CONFIG.REQUEST_COUPON_URL');
        }else if($service == 4){//论酒
            $serviceurl = env('config.REQUEST_LUNJIU_URL');
        }else if($service == 5){//兔头
            $serviceurl = env('config.REQUEST_RABBIT_URL');
        }
        $requesturl = $serviceurl.$url;
        
        //header组装
        $configs = [
            'headers' => $header
        ];
        //body参数组装
        $body = $body;
        if($request_type == 1){//判断请求类型
            $info = $commonHttp->httpPost($requesturl, $body, $configs);
        }else if($request_type == 2){
            //$info = $commonHttp->httpGet($requesturl, $body, $configs);
            $info = file_get_contents($requesturl.$body);
        }
        return json_decode($info,true);
    }


    /**
     * 获取用户及扩展信息
     * 
     * @param array $header 头部参数
     * @param string $uid 用户id
     * @param string $uuid 唯一uuid/设备号
     * @return array $info 返回结果
     * 
     */
    public function getUidAllInfo($header, $uid, $uuid){
        //请求地址 
        $requesturl = '/user/v3/profile/getUserInfo';
        //body参数组装
//        $body = [
//            'uid' => $uid,//用户id
//            'fields' => 'nickname,avatar_image,exps,isdisabled,end_illegal,start_illegal,limits,signs,birthday,is_block_community,score',//用户id
//            'uuid'=> $uuid//唯一uuid/设备号
//        ];
        $body = '?uid='.$uid.'&limit=1&page=1&field=uid,nickname,avatar_image,user_level,certified_info,is_disabled,is_delete,mute_status,mute_end_time,is_pushconbtn';

        //查询微博对应的用户信息
        $userinfoarr = $this->PubForward($header, $body, $requesturl, 1,2);
        $data = !empty($userinfoarr['data']['list'])?$userinfoarr['data']['list']:$userinfoarr['data'];
        return $data;
    }

    /**
     * 批量获取用户及扩展信息
     * 
     * @param array $header 头部参数
     * @param string $uid 用户id
     * @return array $info 返回结果
     * 
     */
    public function getUserInfoArr($header, $uidarr){
        //请求地址
        $requesturl = '/user/v3/profile/getUserInfo';
        //body参数组装
//        $body = [
//            'uid' => arrayToStr($uidarr),//用户id
//            'limit'=> count($uidarr),
//            'page' => 1,
//        ];
        $body = '?uid='.arrayToStr($uidarr).'&field=uid,nickname,avatar_image,user_level,certified_info,is_pushconbtn,type';
        
        //查询微博对应的用户信息
        $userinfoarr = $this->PubForward($header, $body, $requesturl, 1,2);
        $data = !empty($userinfoarr['data']['list'])?$userinfoarr['data']['list']:$userinfoarr['data'];
        return $data;
    }


    /**
     * 上传图片
     *
     * @param array $header 头部参数
     * @param array $files 图片信息
     * @param string $filename 图片存放文件夹名称
     * @return array $info 返回结果
     *
     */
    public function UploadImg($header, $files, $filename){
        //请求地址
        $requesturl = 'upload/image';
        //body参数组装
        $body = [
            'file' => json_encode($files),//用户id
            'service'=> 'user',//文件夹名称（服务名称）
            'module'=> $filename,//图片存放文件夹名称
        ];

        //发起请求
        $imgarr = $this->PubForward($header, $body, $requesturl, 5);
        if($imgarr['status'] == 'success'){
            return $imgarr['data'];
        }else{
            $this->throwError('图片上传失败');
        }

    }


    /**
     * 微博图片处理
     *
     * @param string $image 图片
     * @return array $imgarr 图片
     */
    public function getWeiboImg($image){
        $imgarr = [];
        if($image){
            $OSS_IMG_URL = $image?env('OSS.ALIURL'):'';
            $type_data = explode(',',$image);
            foreach($type_data as $tkey => $tval){
                $img = $OSS_IMG_URL.$tval;
                $imgarr[] = $img;
            }
        }

        return $imgarr;
    }

    /**
     *
     * @param $requestParams
     * @param $rule
     * @throws exception\BusinessException
     */
    public function checkRequestParams($requestParams,$rule)
    {
        $validate = new Validate();
        $result = $validate->failException(false)->check($requestParams,$rule);
        if(true !== $result){
            $this->throwError($validate->getError());
        }
    }

    /**
     * 验证用户是否登录返回解析结果
     * @param $token
     * @return mixed
     * @throws exception\BusinessException
     */
    public function checkUserToken($token)
    {

        if (empty($token)) {
            $this->throwError('请先登录', '777');
        }

        //验证token是否过期
        $tokenCheckResult = (new JwtUtil())->checkRedisToken($token);

        if ($tokenCheckResult['flag'] != 1) {
            $this->throwError($tokenCheckResult['msg'], '777');
        }

        $analysisResult = (new JwtUtil())->analysisToken($token);

        return $analysisResult;
    }


    /**
     * 单推推送
     *
     * @param array $header 头部参数
     * @param string $param 推送参数
     * @return array $info 返回结果
     *
     */
    public function AppSinglePush($header, $param){
        //请求地址
        $requesturl = 'user/information/AppSinglePush';
        //body参数组装
        $body = [
            'uid' => $param['uid'],//用户id
            'source' => $param['source'],//通知类型 2用户通知（话题、帖子等）3物流通知
            'title' => $param['title'],//标题
            'dataid' => $param['dataid'],//数据或链接（例如：话题id，帖子id等）
            'datatype' => $param['datatype'],//数据类型 1产品 2话题 3帖子 4订单
            'image' => $param['image'],//图片
            'content' => $param['content'],//内容
            'path' => $param['path'],//路由
            'name' => $param['name'],//地址名称
            'push_type_id' => $param['push_type_id'],//模板类型
        ];
        //查询微博对应的用户信息
        $userinfoarr = $this->PubForward($header, $body, $requesturl, 1);

        return $userinfoarr['data'];
    }


    /**
     * 获取用户黑名单
     *
     * @param array $header 头部参数
     * @param string $uuid uuid
     * @return array $info 返回结果
     *
     */
    public function getBlacklistUsers($header,$uuid){
        //请求地址
        $requesturl = 'user/userinfo/blacklist';
        //body参数组装
        $body = [
            'uuid'=> $uuid//唯一uuid/设备号
        ];
        //查询微博对应的用户信息
        $userallinfo = $this->PubForward($header, $body, $requesturl, 1);
        return $userallinfo['data'];
    }

    /**
     * content 敏感词验证
     * @param array $header 头部参数
     */
    public function filterSensitiveWords($content)
    {

        $mg_param = [
            'is_ress'=>2,
            'source'=>2,
            'content'=>$content
        ];
        $result = $this->PubForward([],$mg_param,"/api/mall/sensitive/filterSensitiveWords",2);
        if($result['status'] == 'success' && !empty($result['data']['sensitive'])){
            return $result['data']['sensitive']; //铭感词
        }

        return false;
    }

}
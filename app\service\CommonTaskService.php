<?php

namespace app\service;

use app\model\ArticleModel;
use think\facade\Cache;

class CommonTaskService
{

    //酒闻训练 检查下面代码是否可以优化
    public static function wincNewsEmbeddings()
    {
        $field = "id,title,adminid,img,viewnums";
        $where[] = ['img','<>',''];
        $where[] = ['status','=',1];
        $where[] = ['id','>',2800];
//        Cache::set("wincNewsEmbeddingsNums", 0, 3600);
        ArticleModel::field($field)->where($where)->chunk(100, function ($articles) {
            $articles = $articles->toArray();

            $uids = array_unique(array_column($articles, 'adminid'));
            $urlString = '?uid='.implode(',',$uids).'&field=nickname,avatar_image,uid';
            $userinfo = (new ArticleService())->PubForward($header=[], $urlString, '/user/v3/profile/getUserInfo', 1,2);
            $userkey = array_column($userinfo['data']['list'],null,'uid');


            $failds = [];$i = 0;
            foreach ($articles as $article) {//curl 发送数据

                echo $article['id'];
                if(!isset($userkey[$article['adminid']])){
                    continue;
                }
                $item = [
                    'id' => (int)$article['id'],
                    'title' => $article['title'],
                    'banner_img' => $article['img'],
                    'user_img' => $userkey[$article['adminid']]['avatar_image'],
                    'user_name' => $userkey[$article['adminid']]['nickname'],
                    'pageviews' => (int)$article['viewnums']
                ];
                $info = ['eid' => (int)$article['id'], 'source' => 'news', 'data' => json_encode($item), 'is_delete' => 0];
                $url = env('ITEM.RECOMMEND_URL')."/go-recommend/v3/second/syncPool";//地址
                $result = @http_post_json($url, json_encode($info));

                $result = json_decode($result, true);
                if($result['error_code'] != 0){
                    array_push($failds, $article['id']);
                }
                $i +=1;
            }
            $nums = Cache::get("wincNewsEmbeddingsNums")??0;

            Cache::set("wincNewsEmbeddingsNums", ($nums+$i), 3600);
        });
        $nums = Cache::get("wincNewsEmbeddingsNums");
        echo $nums;
    }


}
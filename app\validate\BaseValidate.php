<?php
declare (strict_types=1);

namespace app\validate;

use app\ApiResponse;
use think\Exception;
use think\facade\Request;
use think\Validate;

class BaseValidate extends Validate
{
    use ApiResponse;
    public function goCheck($data = '')
    {
        //实例化请求对象
        $requestObj = Request::instance();
        //如果传入为空则获取请求里的参数
        empty($data) && $data = $requestObj->param();
        if ($this->check($data)) {
            //如果验证通过了
            return true;
        } else {
            //如果验证没通过
            $error = $this->getError();
            //抛出异常
            $this->throwError($error,'10002',200);
        }
    }
}

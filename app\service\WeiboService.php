<?php

namespace app\service;

use app\BaseService;
use app\model\WeiboDiggModel;
use app\model\WeiboModel;
use app\model\CareasModel;
use app\CommonHttpRequest;
use app\model\WeiboCommentModel;
use Config;
use think\facade\Db;

class WeiboService extends BaseService {


    /**
     * 微博首页列表数据
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function getinvitationList($param,$header){        
        $securitycheckval = $header['securitycheckval'];//token值
        
        //验证token，token过期重新授权登录(未登陆可以不验证token)
        if($securitycheckval || $securitycheckval != ''){
            $checkInfo = $this->checkRedisToken($securitycheckval);//验证
            $userinfo = $checkInfo['data'];
            //查询用户信息

            $myuserinfonow = $this->getUidAllInfo($header, $userinfo['uid'], $param['uuid']);
            $param['is_block_community'] = $myuserinfonow['is_block_community'];//是否拉黑 1是 0否
            $myuserinfo = $userinfo['loginname'];//电话号码
            $param['uid'] = $userinfo['uid'];//用户id
        }

        //查询黑名单用户
        $BlacklistUsers = $this->getBlacklistUsers($header,$param['uuid']);
        $blackUid = array_column($BlacklistUsers,'uid');
        $param['blackUid'] = !empty($blackUid)?$blackUid:[0];//黑名单用户id

        if($param['limit'] > 30){
            $this->throwError('分页数量最大不能超过30条');
        }

        //查询数据
        $weiboModel = new WeiboModel();
        $data = $weiboModel->getinvitationList($param);
        $arr = [];
        if(!empty($data)){
            $weiboidarr = array_column($data,'weibo_id');//获取微博id
            $uidarr = array_column($data,'uid');//获取发贴用户id
            
            //获取用户信息     
            $userinfoarr =  $this->getUserInfoArr($header, $uidarr, $param['uuid']); 
            $userinfonew = array_column($userinfoarr, null, 'uid');
            $arr = [];

            //获取点赞信息
            $WeiboDiggModel = new WeiboDiggModel();
            if(!empty($myuserinfo)){
                $weiboidstr = implode(',',$weiboidarr);
                $wherestr = [
                    ['weibo_id','in',$weiboidstr],
                    ['destid','=',0],
                    ['uid','=',$userinfo['uid']],
                    ['type','=',0],
                ];//查询条件
                $diggInfo = $WeiboDiggModel->getBatchDigg($wherestr);
                $diggarr = array_column((array)$diggInfo, null, 'weibo_id');
            }

            foreach($data as $dkey => $dval){               
                $dval['is_digg'] = !isset($diggarr[$dval['weibo_id']]['weibo_id'])?1:0;//是否可以点赞 1是 0否
                
                //微博图片处理
                $dval['type_data'] = $this->getWeiboImg($dval['type_data']);
                
                //用户信息处理
                $dval['userinfo'] = !empty($userinfonew[$dval['uid']])?
                [
                    'nickname'=>$userinfonew[$dval['uid']]['nickname'],
                    'avatar_image'=>$userinfonew[$dval['uid']]['avatar_image'],
                    'ulevel'=>$userinfonew[$dval['uid']]['user_level'],
                    'certification_level'=>$userinfonew[$dval['uid']]['certified_info'],
                ]
                :(object)[];
               
                $dval['ctime'] = date('Y-m-d H:i:s',$dval['ctime']);
                checkIsPhoneToReplace($dval['content'],1);
                $arr[] = $dval;
            }
        }
        //帖子批量添加浏览量
        //控制开启批量添加流量量的config
//        $weibo_views_add = Db::connect('mall')->name('config')->where('name','weibo_views_add')->value('value');
//        if(!empty($weibo_views_add)){
//            $weiboId = array_column($data,'weibo_id');
//            $this->batchViewAdd($weiboId);
//        }
        $msg['flag'] = 1;
        $msg['msg'] = '查询成功';
        $msg['data'] = $arr;

        return $msg;
    }
    

    /**
     * 微博关注、粉丝、帖子数据统计
     *     
     * @param string $header header参数
     * @param array $param 查询条件参数
     * @return array $msg 返回信息
     */
    public function getCountWeiboData($header,$param){
        $securitycheckval = $header['securitycheckval'];//token值

        //验证token，token过期重新授权登录(未登陆可以不验证token)
        if($securitycheckval || $securitycheckval != ''){
            $checkInfo = $this->checkRedisToken($securitycheckval);//验证
            $userinfo = $checkInfo['data'];
            //查询用户信息
            //$myuserinfo = $this->getUidAllInfo($header, $userinfo['uid'], $param['uuid']);
            $myuserinfo = $userinfo['loginname'];//电话号码
        }       

        //查询数据
        $weiboModel = new WeiboModel();
        if(!empty($myuserinfo)){            
            $data = $weiboModel->getCountWeiboData($userinfo['uid']); 
        }else{
            $data['follow_number'] = 0;//关注
            $data['fans_number'] = 0;//粉丝
            $data['invitation_number'] = 0;//帖子
        }   

        $msg['flag'] = 1;
        $msg['msg'] = '查询成功';
        $msg['data'] = $data;

        return $msg;  
    }
    

    /**
     * 微博帖子酒款、评论点赞
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function doDigg($param,$header)
    {   
        $securitycheckval = $header['securitycheckval'];//token值

        //验证token，token过期重新授权登录(未登陆可以不验证token)
        if($securitycheckval || $securitycheckval != ''){
            $this->checkRedisToken($securitycheckval);//验证

            $userinfo = $this->analysisToken($securitycheckval);//解析token  
            //查询用户信息
            $myuserinfo = $this->getUidAllInfo($header, $userinfo['uid'], $param['uuid']);
            //$myuserinfo = $userinfo['loginname'];//电话号码
            if(empty($myuserinfo)){
                $this->throwError('请绑定手机');
            }          
        }else{
            $this->throwError('请重新登录');
        } 

        $weiboModel = new WeiboModel();
        $diggModel = new WeiboDiggModel();

        $flag = 0;//点赞标识 1成功 0失败
        $weibo_id = $param['weibo_id'];
        $uid = $userinfo['uid'];
        $type = empty($param['type'])?0: $param['type'];

        if($type==0){// 赞微博或酒 
            //查询微博信息
            $fields = 'a.weibo_id,a.uid,b.title,a.content';
            $weiboinfo = $weiboModel->getWeiboTopic($weibo_id,$fields);
            //$weibouserInfo = $this->getUidAllInfo($header, $weiboinfo['uid'], $param['uuid']);

            $save = array('uid' => $uid, 'addtime' => time());//点赞时日志记录字段           
            //差查询当前微博信息
            $weibo = $weiboModel->getById($weibo_id, ["content", "wid", "uid"]); 
            if(empty($weibo)){//判断帖子信息是否存在
                $this->throwError('帖子信息不存在');
            }      
            
            $wid = $weibo['wid'];//酒款id        
            
			$save['touid'] = $weibo['uid'];//被赞的人
			if($wid>0 && $wid != 999999999){//判断酒款是否存在
                //是酒
				if($wid>20000000){
                    $wine = $diggModel->getWinecsn($wid);//酒款子表信息查询
                }else{
                    $wine = $diggModel->getWinec($wid);//酒款主表信息查询
                }
                $diggarr = [
                    "weibo_id" => $weibo_id,//帖子id
                    "destid" => $wid,//目标id
                    "uid" => $uid,//执行赞的人
                    "type" => 2//0赞微博1赞评论2赞酒
                ];

                $digginfo = $diggModel->getItem($diggarr);//查询该酒款，该用户是否点过赞

                $save['content'] = serialize($wine);//酒款信息序列化处理，并返回一个字符串
                $save['destid'] = $wid;//目标id
                $save['weibo_id'] = $weibo_id;//帖子id
                $save['type'] = 2;//0赞微博1赞评论2赞酒
            }else{//是帖子                
                $diggarr = [
                    "weibo_id" => $weibo_id,//帖子id
                    "destid" => 0,//目标id
                    "uid" => $uid,//执行赞的人
                    "type" => 0//0赞微博1赞评论2赞酒
                ];
                $digginfo = $diggModel->getItem($diggarr);//查询该帖子，该用户是否点过赞

                $save['weibo_id']=$weibo_id;//帖子id
                $save['destid']=0;//目标id
				$save['content']=$weibo['content'];//微博内容
				$save['type']=0;//0赞微博1赞评论2赞酒
                event('WineYunTask',['uid' => $save['touid'],'change_type' => 6,'header'=>$header]);//todo 赞帖子任务
            }
            
            if(!empty($digginfo)){//该酒款或帖子点赞记录已存在
                $id = $digginfo['id'];
                $is_true = $diggModel->SavediggLog($save,$id,$weibo_id);//点赞记录更新                
            }else{
                $is_true = $diggModel->insertOrUpdate($save,$weibo_id);//插入新的点赞记录并更新微博的点赞人数
            }
            if($is_true){                
                // 推送消息
//                $content = substr($weiboinfo['content'],0,20);
                $content = $weiboinfo['content'];
                if(empty($wid)){
                    $message = $myuserinfo['nickname']."给您的帖子“{$content}”点赞啦！";
                    $title = '帖子';
                }else{
                    $message = $myuserinfo['nickname']."给您的酒款“{$content}”点赞啦！";
                    $title = '酒款';
                }

                $pdata = array(
                    'uid' => $weiboinfo['uid'],//发帖用户id或发表评论用户id
                    'source' => 2,//通知类型 2用户通知（话题、帖子等）3物流通知
                    'title' => $title.'点赞',//标题
                    'dataid' => $weibo_id,//数据或链接（例如：话题id，帖子id等）
                    'datatype' => 3,//数据类型 1产品 2话题 3帖子 4订单 
                    'image' => '',//图片
                    'content' => $message,//内容
                    'path' => 'communityDetail',//路由
                    'name' => '帖子详情',//地址名称
                    'push_type_id' => 3,//模板类型
                );//推送新参数
                $this->AppSinglePush($header,$pdata);//单推
               
                $flag =1;
            }else{
                $flag =0;
            }
        }else{//赞评论

            $commentid = $param['weibo_id'];//评论id
            $save=array('uid'=>$uid,'addtime'=>time());//更新数据

            //查询点赞评论信息
            $comment = $diggModel->getCommentInfo($commentid);

            $weibo_id = $comment['weibo_id'];//帖子id
           
            //查询酒款、帖子评论信息 
            $res = $diggModel->getWeiboDigg($comment['weibo_id'],$commentid,$uid);   
             
            $is_true = false;
            
			if(!empty($comment) && empty($res)){
				$save['type']=1;//0赞微博1赞评论2赞酒
                $save['content']=$comment['content'];//评论内容
                $save['weibo_id']=$comment['weibo_id'];///帖子id
                $save['touid']=$comment['uid'];//用户id
                $save['destid']=$commentid;//评论id
                //插入帖子评论记录
                $is_true = $diggModel->insertCommentLog($save);  
                             
            }
			if($is_true){
				//更新对方被赞次数
                $touid=$comment['uid'];
                //$weibouserInfo = $this->getUidAllInfo($header, $touid, $param['uuid']);

                event('WineYunTask',['uid' => $touid,'change_type' => 7]);//todo 赞帖子任务

                $message = $myuserinfo['nickname']."给您的评论“{$comment['content']}”点赞啦！";
                $pdata = array(
                    'uid' => $touid,//发帖评论用户id
                    'source' => 2,//通知类型 2用户通知（话题、帖子等）3物流通知
                    'title' => '评论点赞',//标题
                    'dataid' => $weibo_id,//数据或链接（例如：话题id，帖子id等）
                    'datatype' => 3,//数据类型 1产品 2话题 3帖子 4订单 
                    'image' => '',//图片
                    'content' => $message,//内容
                    'path' => 'communityDetail',//路由
                    'name' => '帖子详情',//地址名称
                    'push_type_id' => 3,//模板类型
                );//推送新参数
                $this->AppSinglePush($header,$pdata);//单推
                
                $flag =1;
            }else{
                $flag =0;
            }

        }
        if($flag == 1){
            $msg['flag'] = 1;
            $msg['msg'] = '点赞成功';
        }else{
            $msg['flag'] = 0;
            $msg['msg'] = '已点过赞';
        }

        return $msg;
        
    }


    /**
     * 微博发表评论
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function MakeCommentOn($param,$header){
        if(empty($param['is_admin'])){ //后台接口
            $securitycheckval = $header['securitycheckval'];//token值
            //验证token，token过期重新授权登录(未登陆可以不验证token)
            if($securitycheckval || $securitycheckval != ''){
                $this->checkRedisToken($securitycheckval);//验证

                $userinfo = $this->analysisToken($securitycheckval);//解析token
                //查询用户信息
                $myuserinfonew = $this->getUidAllInfo($header, $userinfo['uid'], $param['uuid']);
                $myuserinfo = $userinfo['loginname'];//电话号码
                $is_block_community = $myuserinfonew['is_block_community'];//是否拉黑 1是 0否

                if(empty($myuserinfo)){
                    $this->throwError('请绑定手机');
                }
            }else{
                $this->throwError('请先登陆');
            }
            $uid = $userinfo['uid'];//用户id
        }else{
            $uid = $param['uid'];//用户id
            $header['securitycheckval'] = '';
        }

        $weiboModel = new WeiboModel();
        $WeiboCommentModel = new WeiboCommentModel();

		$weibo_id = $param['weibo_id'];//帖子id
        //敏感词判断
        $word_state = $this->filterSensitiveWords($param['content']);
		$content = $param['content'];//评论内容、
		$address = isset($param['address'])?$param['address']:'';//地址
        $score  = isset($param['score'])?$param['score']:'';//评分
        $reply_uid = isset($param['reply_uid'])?$param['reply_uid']:'';//被回复评论的用户id
        $fcontent = isset($param['fcontent'])?$param['fcontent']:'';//被回复的评论内容
        $reply_comment_id = isset($param['reply_comment_id'])?$param['reply_comment_id']:'';//被回复的评论id(未传入表示第一条评论)

        if(empty($param['is_admin'])){
            //验证用户是否被禁言
            $userallinfo = $this->verfiyillegal($header,$uid,$param['uuid']);
            //查询用户限制 1禁止评论 4可见所有评论 5酒款预购黑名单 6社区评论只有自己可见
            $limits = $userallinfo['limits'];
            if(!empty($limits)){
                $is_status = 0;//状态 0 禁用（社区评论只有自己可见） 1启用
            }else{
                $is_status = 1; //状态 0 禁用（社区评论只有自己可见） 1启用
            }

            //微博评论关键词屏蔽
//            $weibo_sensitive_words = $this->weibo_sensitive_words($header,$param['uuid']);
//            $weibo_sensitive_words = explode('|', $weibo_sensitive_words);
//            foreach ($weibo_sensitive_words as $k => $v) {
//                if(strpos($content,$v) !== false){
//                    $is_status = 0; //状态 0 禁用（社区评论只有自己可见） 1启用
//                }else{
//                    $is_status = 1; //状态 0 禁用（社区评论只有自己可见） 1启用
//                }
//            }
        }

        $save=array();//评论入库信息        
        //评论图片信息处理
		$imgArr = '';
		$save['type_data'] = $imgArr;//json_encode($imgArr);//图片内容
		$save['uid']=$uid;//评论用户id		
		$save['reply_comment_id']= $reply_comment_id?$reply_comment_id:0 ;//被回复的评论id
		$save['reply_uid']=$reply_uid;//被回复评论的用户id
		$save['weibo_id']=$weibo_id;//帖子id
		$save['content']=$content;//评论内容
		$save['address']=empty($address)?"":$address;//评论地址
		$save['fcontent']=empty($fcontent)?"":$fcontent;//回复内容
		$save['ctime']=time();//评论时间
		$save['isdel']=0;//是否删除 1是 0否
		if(isset($score)){
			$save['score']=round($score);//对评分进行四舍五入处理
        }

        $save['is_status'] = 0;//状态 0 禁用（社区评论只有自己可见） 1启用
		if(empty($param['is_admin'])){
            //$save['is_status'] = $is_status;//状态 0 禁用（社区评论只有自己可见） 1启用
        }else{
//            if($word_state == true) $this->throwError('评论内容中存在敏感词，不允许开放出来');
            $save['is_status'] = 1;
        }

        //查询微博信息
        $fields = 'a.weibo_id,a.uid,b.title';
        $weiboinfo = $weiboModel->getWeiboTopic($weibo_id,$fields);

        //判断是评论帖子还是回复评论

        //todo 评论帖子
        if(empty($reply_comment_id)){//被回复的评论id如果为空则是评论帖子
            //查询帖子用户
            $pubUid = $weiboModel->getById($weibo_id,["uid","is_status"]);
            $pubuserinfo = $this->getUidAllInfo($header, $pubUid['uid'], $param['uuid']);
            if($uid != $pubUid['uid']){
                if($pubUid['is_status'] != 0){
                    $this->throwError('被回复的帖子不存在1');
                }
            }
            //以评论帖子的用户为单位模块
            $temp = array();
            $temp['weibo_id']=$weibo_id;//微博id
            $temp['uid']=$uid;//第一个评论用户id
            $temp['lastuid']=$uid;//最后一个评论用户id
            $temp['lastcontent']=$content;//最后一条评论内容
            $temp['address']=$address;//最后一条评论地址
            $temp['lasttime']=time();//最后一条评论时间
            $temp['type']=0;//评论类型 0微博1资讯
            //添加到评论分块表
            $is_res = $WeiboCommentModel->addBox($temp,$save);

            if(!empty($weiboinfo['title'])){
                $message = "尊敬的".$pubuserinfo['nickname']."，你发布的【".$weiboinfo['title']."】话题帖子，有了新的评论，立即打开查看【酒云网】";
            }else{
                $message = "尊敬的".$pubuserinfo['nickname']."，你发布的帖子，有了新的评论，立即打开查看【酒云网】";
            }
            $commentType = 1; //评论

        }else{//不为空，则是回复评论
            //todo 回复帖子
            //查询评论用户
            $pubUid = $WeiboCommentModel->getIdComment($reply_comment_id);
            $pubuserinfo = $this->getUidAllInfo($header, $pubUid['uid'], $param['uuid']);

            $temp = array();
            $temp['lastuid']=$uid;//最后一个评论用户id
            $temp['lastcontent']=$content;//最后一条评论内容
            $temp['address']=$address;//最后一条评论地址
            $temp['lasttime']=time();//最后一条评论时间
            //查询该评论属于哪一个评论分块
            $boxid = $pubUid['boxid'];
            if($uid != $pubUid['uid']){
                if($pubUid['is_status'] != 1){
                    $this->throwError('被回复的评论不存在');
                }
            }

            //更新该评论分块的内容
            $is_res = $WeiboCommentModel->saveBox($boxid,$temp,$save);

            if(!empty($weiboinfo['title'])){
                $message = "尊敬的".$pubuserinfo['nickname']."，你发布的【".$weiboinfo['title']."】话题帖子评论，有了新的回复，立即打开查看【酒云网】";
            }else{
                $message = "尊敬的".$pubuserinfo['nickname']."，你发布的帖子评论，有了新的回复，立即打开查看【酒云网】";
            }

            $commentType = 2; //回复
        }

		if(!$is_res){
            $msg['flag'] = 0;
            $msg['msg'] = '评论失败';
        }else{//评论成功
		    //判断当前用户是否已被加入敏感用户人群，加入了则屏蔽评论
            $s_data = array();
            $s_data['uid'] = $uid;
            $s_data['comment_id'] = (int)$is_res;
            $s_data['type'] = $commentType;
            $this->checkUidSensitive($s_data, $header);
            //返回参数处理
            $data['comment_id'] = (int)$is_res;//评论id
            $data['reply_comment_id'] = (int)$reply_comment_id;//被回复的评论id
            $data['isdel'] = 0;//是否删除 1是 0否
            $data['diggnums'] = 0;//赞次数
            $data['address']=$address;//位置
            checkIsPhoneToReplace($content,1);
            $data['content']=$content;//评论内容
            //$data['images']=array();//图片
            $data['ctime']=date('Y-m-d H:i:s',time());//评论时间

            //评论用户信息
            $data['userinfo'] = array(
                'uid' => $uid,//评论用户id
                'nickname' => empty($userallinfo['nickname'])?"":$userallinfo['nickname'],//评论用户昵称
                'avatar_image' => empty($userallinfo['avatar_image'])?"":$userallinfo['avatar_image'],//评论用户头像
                'ulevel' => !empty($userallinfo['user_level'])?$userallinfo['user_level']:0,//评论用户等级
                'certification_level' => empty($userallinfo['certified_info'])?"":$userallinfo['certified_info'],//评论用户认证等级
            );
           
            $data['weibo_username'] = empty($pubuserinfo['nickname'])?"":$pubuserinfo['nickname'];//被评论的用户名称
            
            $fields = 'wid,type,is_status,content';//查询字段
            //查询帖子信息
            $weibo = $weiboModel->getById($weibo_id, $fields);
            $wid = $weibo['wid'];//酒款id
           

            if($wid>0){//酒款id大于0，表示是酒款评论
                //查询是否已品鉴 0未评鉴 1已评鉴
                $pub_info = $WeiboCommentModel->selectJudgeinfo($wid,$uid);
                if(empty($pub_info['judgeinfo']) && empty($pub_info['commentinfo'])){
                    $data['hastaste']='0';//未评鉴
                }else{
                    $data['hastaste']='1';//已品鉴
                }
            }else{
                $data['hastaste']=0;//未评鉴
            }

            $msg['flag'] = 1;
            $msg['msg'] = '发布成功';
            $msg['data'] = $data;
            if(empty($param['is_admin'])){
                //评论推送
                $pdata = array(
                    'uid' => $pubUid['uid'],//用户id
                    'source' => 2,//通知类型 2用户通知（话题、帖子等）3物流通知
                    'title' => '帖子评论',//标题
                    'dataid' => $weibo_id,//数据或链接（例如：话题id，帖子id等）
                    'datatype' => 3,//数据类型 1产品 2话题 3帖子 4订单
                    'image' => '',//图片
                    'content' => $message,//内容
                    'path' => 'communityDetail',//路由
                    'name' => '帖子详情',//地址名称
                    'push_type_id' => 3,//模板类型
                );//推送新参数

                //发评论用户不是黑名单用户且该条帖子未被屏蔽才推送
                /*if((isset($is_block_community) && $is_block_community == 0) && $weibo['is_status'] == 0){
                    $this->AppSinglePush($header,$pdata);//单推
                }*/
                //推送钉钉,评论
                if(!empty($commentType)){
//                    $publishUid = $weiboinfo['uid'];
                    $publishUid = $uid;
                    if($commentType == 1){
                        event('DingTalkTask',['type'=>'dingTalk','param'=>[
                            ['type'=>'2','uid'=>$publishUid,'content'=>"帖子内容：".$weibo['content']." <br/> ★ 新增评论：".$content,
                             'id'=>$data['comment_id']]
                        ]]);
                    }elseif($commentType == 2){

                        event('DingTalkTask',['type'=>'dingTalk','param'=>[
                            ['type'=>'2','uid'=>$publishUid,'content'=>"帖子内容：".$pubUid['content']." <br/> ★ 新增回复 ".$content,
                             'id'=>$data['comment_id']]
                        ]]);
                    }

                }
            }

        }
		
		
		return $msg;
    }

    public function checkUidSensitive($data,$header)
    {
        $url = 'mall/sensitiveuser/addSensitive';
        $data['source'] = 1;
        $result = $this->PubForward($header, $data, $url, 2);
        if (!empty($result['data']['is_sensitive_user']) && $result['data']['is_sensitive_user']==1) {
            //如果该评论用户是敏感用户，则将评论或回复屏蔽
            Db::name('weibo_comment')->where(['comment_id'=>$data['comment_id']])->update(['is_status'=>0]);
        }
    }
    /**
     * 删除帖子评论
     *
     * @param array $param 提交参数
     * @param array $header 头部参
     * @return array $res 返回结果
     */
    public function delWeiboComment($param,$header){

        $securitycheckval = $header['securitycheckval'];//token值
        //验证token，token过期重新授权登录(未登陆可以不验证token)
        if($securitycheckval || $securitycheckval != ''){
            $this->checkRedisToken($securitycheckval);//验证
            $userinfo = $this->analysisToken($securitycheckval);//解析token
        }else{
            $this->throwError('请先登陆');
        }

        if (empty($param['comment_id'])) {
            $this->throwError('请选择要删除的评论');
        }
        
        $WeiboCommentModel = new WeiboCommentModel();
        $comment = $WeiboCommentModel->getCommentInfo($param['comment_id']);
        if (empty($comment)) {
            $this->throwError('评论不存在');
        }
        if (empty($comment['uid']) || $comment['uid']!=$userinfo['uid']) {
            $this->throwError('只能删除自己的评论');
        }
        if (!empty($comment['isdel']) && $comment['isdel']=1) {
            $this->throwError('评论已删除');
        }

        $where = explode(',',$param['comment_id']);//更新条件
        
        $is_res = $WeiboCommentModel->delWeiboComment($where);
        if($is_res){
            $res['flag'] = 1;
            $res['msg'] = '操作成功';
        }else{
            $res['flag'] = 0;
            $res['msg'] = '操作失败';
        }

        return $res;
    }
    /**
     * 查询微博评论关键词屏蔽
     * @param  integer $uuid 唯一uuid/设备号
     * @return string $res 关键词
     */
    public function weibo_sensitive_words($header,$uuid){
        //请求地址 
        $requesturl = 'user/systemsconfig/getConfigInfo';
        //body参数组装
        $body = [
            'fields' => 'app_value',//查询参数
            'app_key' => 'weibo_sensitive_words',//微博关键词配置的键
            'uuid'=> $uuid//唯一uuid/设备号
        ];
        //查询微博对应的用户信息
        $config = $this->PubForward($header, $body, $requesturl, 1);
        return $config['data']['app_value'];
    }

    /**
	 * 验证用户是否被禁言或被封号
	 * [verfiyillegal description]
	 * @param  integer $uid [description]
	 * @return [type]       [description]
	 */
	public function verfiyillegal($header,$uid=0,$uuid){
        $userinfo = $this->getUidAllInfo($header, $uid, $uuid);
        $userinfo = $userinfo[0];
        $msg = true;
		if(empty($userinfo)){
            $msg = '抱歉！账号不存在';            
		}elseif ($userinfo['is_disabled']==1) {
			$msg =  "抱歉！您已被禁用";
		}elseif($userinfo['mute_status']==1){
			$days=time();
			if($userinfo['mute_end_time']<=$days){
				$msg = "抱歉！您已被禁言,于".date('m-d H:i',$userinfo['mute_end_time'])."解除";
			}
        }
        if($msg != true){
            return $this->throwError($msg);
        }

		return $userinfo;
    }

        
    
    /**
     * 微博详情
     *
     * @param array $param 查询使用参数
     * @param array $header 头部参数
     * @return array $msg 查询结果
     */
    public function getWeiboDetail($param,$header)
    {

        $securitycheckval = $header['securitycheckval'];//token值

        $del_flag = 0;//是否显示删除图标 0不显示 1显示
        $myuserinfo = [];//用户信息
        $is_digg = 1;//是否可以点赞 1是 0否

        //验证token，token过期重新授权登录(未登陆可以不验证token)
        if($securitycheckval || $securitycheckval != ''){
            $this->checkRedisToken($securitycheckval);//验证

            $myinfo = $this->analysisToken($securitycheckval);//解析token 
            //查询用户信息
            $myuserinfonow = $this->getUidAllInfo($header, $myinfo['uid'], $param['uuid']);
            $myuserinfo = $myinfo['loginname'];//电话号码            
            if(empty($myuserinfo)){
                $is_digg = 1;//是否可以点赞 1是 0否
            }
            $param['uid'] = $myinfo['uid'];//用户id
            $param['is_block_community'] = $myuserinfonow['is_block_community'];//是否拉黑 1是 0否
        }

        $WeiboModel = new WeiboModel();

        //查询黑名单用户
        $BlacklistUsers = $this->getBlacklistUsers($header,$param['uuid']);
        $blackUid = array_column($BlacklistUsers,'uid');
        $param['blackUid'] = !empty($blackUid)?$blackUid:[0];//黑名单用户id

        if (isset($param['uid']) && $param['uid'] != ''){//如果用户id存在，屏蔽的帖子也显示
            if(!empty($param['blackUid'])){
                if(isset($param['is_block_community']) && $param['is_block_community'] == 1){//判断登录使用户是否被加入黑名单（拉黑社区发帖及评论 1是 0否）
                    foreach( $param['blackUid'] as $k=>$v) {
                        if($param['uid'] == $v) unset($param['blackUid'][$k]);
                    }
                    $blackUid = !empty($param['blackUid'])?$param['blackUid']:[0];//黑名单用户（不包括登录用户自己）
                }else{
                    $blackUid = $param['blackUid']??0;//黑名单用户
                }
            }
            $map1 = [
                ['a.isdel','=',0],
                ['a.is_status','=',0],
                ['a.uid','not in',$blackUid??0]
            ];
            $map2 = [
                ['a.isdel','=',0],
                ['a.is_status','=',1],//屏蔽
                ['a.uid','=',$param['uid']],
            ];
            $where = [$map1,$map2];
        }else{
            $blackUid = !empty($param['blackUid'])?$param['blackUid']:[0];
            $map1 = [
                ['a.isdel','=',0],
                ['a.is_status','=',0],//未屏蔽
                ['a.uid','not in',$blackUid??0]
            ];
            $where = [$map1];
        }

        if (isset($param['uid']) && $param['uid'] != ''){//如果用户id存在，屏蔽的评论也统计
            $blackUidstr = implode(',',$blackUid);//黑名单用户
            $commentWhere = "(c.weibo_id=a.weibo_id AND c.isdel=0 AND c.is_status = 1 AND c.uid NOT IN ({$blackUidstr})) OR (c.weibo_id=a.weibo_id AND c.isdel=0 AND c.is_status=0 AND c.uid={$param['uid']})";
        }else {
            $blackUidstr = implode(',',$blackUid);//黑名单用户
            $commentWhere = "c.weibo_id=a.weibo_id AND c.isdel=0 AND c.is_status = 1 AND c.uid NOT IN ({$blackUidstr})";
        }

        //帖子评论统计
        $commentNum = Db::name('weibo_comment')
            ->alias('c')
            ->fetchSql(true)
            ->whereRaw($commentWhere)
            ->count();

        //查询字段
        $fields = [
            'a.weibo_id',
            'a.uid',
            'a.content',
            'FROM_UNIXTIME(a.ctime) as ctime',
            'a.type_data',
            'a.address',
            'a.viewnums',
            'a.diggnums',
            'a.topicids',
            '('.$commentNum.') as commentnums',
            'b.title'
        ];

        //查询微博信息
        $weiboinfo = $WeiboModel->getWeiboTopic($param['weibo_id'],$fields);
        if(empty($weiboinfo)){
            $this->throwError('帖子已被删除');
        }

        if(($securitycheckval || $securitycheckval != '') && ($myinfo['uid'] == $weiboinfo['uid'])){//当前用户就是发帖用户
            $del_flag = 1;
        }

        $weiboinfo['del_flag'] = $del_flag;//是否显示删除图标 0不显示 1显示

        //更新微博的浏览次数
        $save = array(
            'viewnums' =>	Db::raw('viewnums+1')
        );//更新字段
        $WeiboModel->SaveWeibo($save,$param['weibo_id']);

        //判断帖子是否已经点赞
        
        $WeiboDiggModel = new WeiboDiggModel();
        if(!empty($myuserinfo)){
            $wherestr = "uid={$myinfo['uid']} and weibo_id={$param['weibo_id']} and type=0";
            $digg = $WeiboDiggModel->getDigg($wherestr);
            if(!empty($digg)){
                $is_digg = 0;//是否可以点赞 1是 0否
            }else{
                $is_digg = 1;//是否可以点赞 1是 0否
            }
        } 
        

        //微博图片处理
        $weiboinfo['type_data'] = $this->getWeiboImg($weiboinfo['type_data']);

        //是否可以点赞 1是 0否
        $weiboinfo['is_digg'] = $is_digg;

        //分享地址
        $weiboinfo['share_link'] = Config('config')['WEIBO_SHARE_URL']."/web-static/details/communityDetail.html?id={$param['weibo_id']}";

        //详情地址
        $weiboinfo['detail_link'] = Config('config')['WEIBO_SHARE_URL']."/web-static/communityDetail/{$param['weibo_id']}";
        
        //查询用户信息
        $userinfo = $this->getUidAllInfo($header, $weiboinfo['uid'], $param['uuid']);
        $weiboinfo['userinfo'] = array(
            'nickname' => isset($userinfo['nickname'])?$userinfo['nickname']:'',//昵称
            'avatar_image' => isset($userinfo['avatar_image'])?$userinfo['avatar_image']:'',//头像
            'ulevel' => isset($userinfo['user_level'])?$userinfo['user_level']:0,//用户等级
            'certification_level' => isset($userinfo['certified_info'])?$userinfo['certified_info']:0//认证等级
        );

        checkIsPhoneToReplace($weiboinfo['content'],1);

        $weiboinfo = (new WeiboModel())->weiboIsfollow([$weiboinfo],$param['uid']??0)[0];
        $msg['flag'] = 1;
        $msg['msg'] = '查询成功';
        $msg['data'] = $weiboinfo;
        return $msg;
    }  


    /**
     * 微博评论列表
     *
     * @param array $param 查询使用参数
     * @param array $header 头部参数
     * @return array $msg 查询结果
     */
    public function getWeiboComment($param,$header){
        $securitycheckval = $header['securitycheckval'];//token值

        //验证token，token过期重新授权登录(未登陆可以不验证token)
        if($securitycheckval || $securitycheckval != ''){
            $checkInfo = $this->checkRedisToken($securitycheckval);//验证
            $userinfologin = $checkInfo['data'];
            //查询用户信息
            $myuserinfonow = $this->getUidAllInfo($header, $userinfologin['uid'], $param['uuid']);
            $myuserinfo = $userinfologin['loginname'];//电话号码
            $param['uid'] = $userinfologin['uid'];//用户id
            $param['is_block_community'] = $myuserinfonow['is_block_community'];//是否拉黑 1是 0否
        }        

        if($param['limit'] > 30){
            $this->throwError('分页数量最大不能超过30条');
        }

        //查询黑名单用户
        $BlacklistUsers = $this->getBlacklistUsers($header,$param['uuid']);
        $blackUid = array_column($BlacklistUsers,'uid');
        $param['blackUid'] = !empty($blackUid)?$blackUid:[0];//黑名单用户id
        
        //数据处理
        $WeiboCommentModel = new WeiboCommentModel();
        $weibocommentInfo = $WeiboCommentModel->getWeiboComment($param);//查询帖子评论

        $commentInfo = $weibocommentInfo['list'];

        //查询评论对应用户的uid
        $WeiboModel = new WeiboModel();
        $wuid = $WeiboModel->getById($param['weibo_id'],['uid']);
        if(empty($wuid)){
            $this->throwError('帖子不存在');
        }

        //查询帖子的用户信息
        $userinfo = $this->getUidAllInfo($header, $wuid['uid'], $param['uuid']);

        $data = [];
        if(!empty($commentInfo)){
            $commentidarr = array_column($commentInfo,'comment_id');//获取微博评论id
            $oneuidarr = array_column($commentInfo,'uid');//获取帖子评论用户id
            $boxidarr = array_column($commentInfo,'boxid');//获取微博评论分块id

            //获取点赞信息
            $WeiboDiggModel = new WeiboDiggModel();            
            if(!empty($myuserinfo)){
                $commentidstr = implode(',',$commentidarr);
                $wherestr = "weibo_id={$param['weibo_id']} and destid in ($commentidstr) and uid={$userinfologin['uid']} and type=1";//查询条件
                $diggInfo = $WeiboDiggModel->getBatchDigg($wherestr);                
                $diggarr = array_column((array)$diggInfo, null, 'destid');
            }
            
            //查询帖子评论的用户信息   
            $oneuserinfoarr =  $this->getUserInfoArr($header, $oneuidarr, $param['uuid']);
            $oneuserinfoarrnew = array_column($oneuserinfoarr, null, 'uid');

            //查询帖子评论的回复评论
            $replycommenInfo = $WeiboCommentModel->getWeiboReplyComment($boxidarr,$param);
            //获取回复评论用户id
            $twouidarr = array_column($replycommenInfo,'uid');
            //查询回复评论的用户信息   
            $twouserinfoarr =  $this->getUserInfoArr($header, $twouidarr, $param['uuid']); 
            $twouserinfoarrnew = array_column($twouserinfoarr, null, 'uid');

            //获取回复评论用户id
            $threeuidarr = array_column($replycommenInfo,'reply_uid');
            //查询被回复评论的用户信息
            $threeuserinfoarr =  $this->getUserInfoArr($header, $threeuidarr, $param['uuid']); 
            $threeuserinfoarrnew = array_column($threeuserinfoarr, null, 'uid');

            foreach($commentInfo as $ckey => $cval){//帖子评论数据
                $cval['weibo_username'] =  $userinfo['nickname']?$userinfo['nickname']:'';//被评论的帖子用户昵称
                $cval['is_digg'] = !isset($diggarr[$cval['comment_id']]['destid'])?1:0;//是否可以点赞 1是 0否

                //是否可以删除 1是 0否
                $cval['is_del'] = 0;
                if (!empty($userinfologin['uid'])) {
                    $cval['is_del'] = $cval['uid']==$userinfologin['uid']?1:0;
                }

                //评论用户信息
                $cval['userinfo'] = isset($oneuserinfoarrnew[$cval['uid']])?
                [
                    'nickname'=>$oneuserinfoarrnew[$cval['uid']]['nickname'],
                    'avatar_image'=>$oneuserinfoarrnew[$cval['uid']]['avatar_image'],
                    'ulevel'=>$oneuserinfoarrnew[$cval['uid']]['user_level'],
                    'certification_level'=>$oneuserinfoarrnew[$cval['uid']]['certified_info'],
                ]
                :(object)[];

                checkIsPhoneToReplace($cval['content'],1);
                //回复评论数据处理
                foreach($replycommenInfo as $rkey => $rval){    
                    $weibouserinfo = (array)$cval['userinfo'];                   
                                      
                    $rval['weibo_username'] = isset($threeuserinfoarrnew[$rval['reply_uid']])?$threeuserinfoarrnew[$rval['reply_uid']]['nickname']:'';//被评论的评论用户昵称

                    //是否可以删除 1是 0否
                    $rval['is_del'] = 0;
                    if (!empty($userinfologin['uid'])) {
                        $rval['is_del'] = $rval['uid']==$userinfologin['uid']?1:0;
                    }

                    //评论回复用户信息处理
                    $rval['userinfo'] = isset($twouserinfoarrnew[$rval['uid']])?
                    [
                        'nickname'=>$twouserinfoarrnew[$rval['uid']]['nickname'],
                        'avatar_image'=>$twouserinfoarrnew[$rval['uid']]['avatar_image'],
                        'ulevel'=>$twouserinfoarrnew[$rval['uid']]['user_level'],
                        'certification_level'=>$twouserinfoarrnew[$rval['uid']]['certified_info'],
                    ]
                    :(object)[];

                    checkIsPhoneToReplace($rval['content'],1);
                    if($cval['boxid'] == $rval['boxid']){ 
                        $cval['replyCommentInfo'][] = $rval;
                    }
                     
                }     
                
                $data[] = $cval;
            }
        }          
        $weibocommentInfo['list'] = $data;

        $msg['flag'] = 1;
        $msg['msg'] = '查询成功';
        $msg['data'] = $weibocommentInfo;

        return $msg;
    }


    /**
     * 删除帖子
     *
     * @param array $param 帖子参数
     * @param array $header 头部参数
     * @return array $msg 返回信息
     */
    public function delWeibo($param,$header){
        $securitycheckval = $header['securitycheckval'];//token值

        //验证token，token过期重新授权登录(未登陆可以不验证token)
        if($securitycheckval || $securitycheckval != ''){
            $this->checkRedisToken($securitycheckval);//验证

            $userinfo = $this->analysisToken($securitycheckval);//解析token  
            //查询用户信息
            //$myuserinfo = $this->getUidAllInfo($header, $userinfo['uid'], $param['uuid']);
            $myuserinfo = $userinfo['loginname'];//电话号码
            if(empty($myuserinfo)){
                $this->throwError('请绑定手机');
            }        
        }else{
            $this->throwError('请先登陆');
        }

        $WeiboModel = new WeiboModel();
        //查询微博信息
        $fields = 'weibo_id,uid';
        $weiboinfo = $WeiboModel->getById($param['weibo_id'],$fields);
        if(empty($weiboinfo)){
            $this->throwError('帖子不存在');
        }
        //判断发帖用户是不是当前用户
        if($weiboinfo['uid'] != $userinfo['uid']){
            $this->throwError('抱歉，您不是发帖人，不能进行此操作');
        }

        //删除帖子
        $save = array(
            'isdel' =>	1
        );//更新字段
        $is_del = $WeiboModel->SaveWeibo($save,$param['weibo_id']);
        if($is_del){
            $msg['flag'] = 1;
            $msg['msg'] = '帖子删除成功';
            //更新发帖数据到es
            event('SynToEs',['id' => $param['weibo_id'],'type'=>3,'operate'=>'delete']);
        }else{
            $msg['flag'] = 0;
            $msg['msg'] = '帖子删除失败';
        }
        return $msg;
    }


    /**
     * 添加关注
     *
     * @param array $param 帖子参数
     * @param array $header 头部参数
     * @return array $msg 返回信息
     */
    public function addFollow($param,$header){
        $securitycheckval = $header['securitycheckval'];//token值

        //验证token，token过期重新授权登录(未登陆可以不验证token)
        if($securitycheckval || $securitycheckval != ''){
            $this->checkRedisToken($securitycheckval);//验证

            $userinfo = $this->analysisToken($securitycheckval);//解析token    
            //查询用户信息
            //$myuserinfo = $this->getUidAllInfo($header, $userinfo['uid'], $param['uuid']);
            $myuserinfo = $userinfo['loginname'];//电话号码
            if(empty($myuserinfo)){
                $this->throwError('请绑定手机');
            }        
        }else{
            $this->throwError('请先登陆');
        }

        $WeiboModel = new WeiboModel();

        //判断用户是否被关注过
        $is_follow = $WeiboModel->selectFollow($param['followuid'],$userinfo['uid']);
        if($is_follow){
            $this->throwError('已关注过');
        }
        $data = array(
            'uid' => $param['followuid'],//被关注的用户id
            'fid' => $userinfo['uid'],//当前用户id
            'addtime' => time(),//关注时间
        );
        $is_add = $WeiboModel->addFollow($data);

        if($is_add){
            $msg['flag'] = 1;
            $msg['msg'] = '关注成功';
        }else{
            $msg['flag'] = 0;
            $msg['msg'] = '关注失败';
        }
        return $msg;
    }


    /**
     * 取消关注
     *
     * @param array $param 帖子参数
     * @param array $header 头部参数
     * @return array $msg 返回信息
     */
    public function delFollow($param,$header){
        $securitycheckval = $header['securitycheckval'];//token值

        //验证token，token过期重新授权登录(未登陆可以不验证token)
        if($securitycheckval || $securitycheckval != ''){
            $this->checkRedisToken($securitycheckval);//验证

            $userinfo = $this->analysisToken($securitycheckval);//解析token 
            //查询用户信息
            //$myuserinfo = $this->getUidAllInfo($header, $userinfo['uid'], $param['uuid']);
            $myuserinfo = $userinfo['loginname'];//电话号码
            if(empty($myuserinfo)){
                $this->throwError('请绑定手机');
            }            
        }else{
            $this->throwError('请先登陆');
        }

        $WeiboModel = new WeiboModel();
        $is_del = $WeiboModel->delFollow($param['followuid'],$userinfo['uid']);

        if($is_del){
            $msg['flag'] = 1;
            $msg['msg'] = '取消关注成功';
        }else{
            $msg['flag'] = 0;
            $msg['msg'] = '取消关注失败';
        }
        return $msg;
    }

    private function batchViewAdd($weiboId)
    {
        $WeiboModel = new WeiboModel();
        return $WeiboModel->batchViewAdd($weiboId);
    }
    
}
<?php
declare (strict_types=1);

namespace app\service\command;


use app\service\elasticsearch\Document;
use app\service\elasticsearch\types\WinecDocument;
use app\service\ElasticSearchService;
use think\facade\Db;


class SynWinecToEsCommand
{
    private $document;
    private $model;

    private function init()
    {
        $this->document = new Document();
    }

    public function exec($model)
    {
        $this->init();
        $this->model = $model;

        $waitSynData = $this->model->where(['type' => 4])->select()->toArray();

        if(empty($waitSynData)){
//            exit;
        }
        $waitSynIdsArr = array_column($waitSynData, 'task_id');
        $reWaitSysData = array_column($waitSynData, null, 'task_id');

        $fields = [
            'wid',
            'md5wid',
            'winename',
            'wename',
            'moddle_image',
            'wyear',
            'minprice',
            'maxprice',
            'priceyear',
        ];

        //$write_type 写入方式， 1单条写入 2批量写入
        $write_type = 2;

        for ($i=1;$i<=17;$i++){

            $page = $i;
            $limit = 50000;
            $data = Db::name('winec')
//            $data = Db::name('winecsn')
                ->field($fields)
                ->where(['status'=>1])
                ->limit(($page-1)*$limit,$limit)
                ->select()->toArray();

            $winecDocuments = [];
            foreach ($data as &$item) {
                $item['resource'] = 'winec';
                $item['effect_time_range'] = ['gte' => 0, 'lte' => 4747881840];
                $item['id'] = $item['wid'];
                $winecDocument = new WinecDocument($item['id']);
                $operate = !empty($reWaitSysData[$item['id']]) ? $reWaitSysData[$item['id']]['operate'] : 'create';
                if($write_type == 1)
                    $operate = $this->checkData($item['id'],$operate);

                if($operate === true){
                    $this->model->where(['task_id' => $item['id']])->delete();
                    continue;
                }
                $winecDocument->setIndex(env('ES.PREFIX').'winec_'.env('ES.ENV'));
                $winecDocument->setOperate($operate);
                $winecDocument->setHit($item);
                $winecDocument->toArray();

                $winecDocuments[] = $winecDocument;
                if($write_type == 1){ //单条写入
                    $result = $this->document->setDocument($winecDocument)->$operate();
                    $this->delData($result);
                }elseif($write_type == 2){ //批量写入
                    if( count($winecDocuments)  == 1000 ){
                        $result = $this->document->bulk($winecDocuments);
                        $winecDocuments = [];
                    }

                }
            }
        }
        if(!empty($winecDocuments)){
            $result = $this->document->bulk($winecDocuments);
        }

    }

    private function delData($result)
    {
        $this->model->where(['task_id' => $result['_id']])->delete();
    }

    public function checkData($id,$operate)
    {
        $searchService = new ElasticSearchService();
        $winec = 'winec';
        $input = [
            'index' => [$winec],
            'match' => [['id' => $id]]
        ];
        $res = $searchService->getDocumentList($input);

        if ($operate == 'update') {// update  delete 先判断数据是否存在，在决定是否入库
            if (empty($res['total']['value'])) {
                $operate = 'create';
            }

        }elseif($operate == 'create'){
            if (!empty($res['total']['value'])) {
                $operate = 'update';
            }

        } elseif ($operate == 'delete') {
            if (empty($res['total']['value'])) {
                return true;
            }
        }
        return $operate;
    }
}

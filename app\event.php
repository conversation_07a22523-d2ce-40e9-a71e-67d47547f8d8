<?php
// 事件定义文件
return [
    'bind'      => [
        'WineYunTask' => 'app\event\WineYunTask',
        'SynToEs' => 'app\event\SynToEs',
        'DingTalkTask' => 'app\listener\DingTalkTask'
    ],

    'listen'    => [
        'AppInit'  => [],
        'HttpRun'  => [],
        'HttpEnd'  => [],
        'LogLevel' => [],
        'LogWrite' => [],
        'WineYunTask' =>['app\listener\WineYunTask'],
        'SynToEs' => ['app\listener\SynToEs'],
        'DingTalkTask' => ['app\listener\DingTalkTask'],
    ],

    'subscribe' => [
    ],
];

<?php
namespace app\service\elasticsearch\types;

/**
 * 创建es mapping
 * Class Document
 * @package App\Services\Elasticsearch
 */

abstract class Document
{
    protected $index;
    protected $type;
    protected $id;
    protected $hit;
    protected $operate;

    public function getIndex()
    {
        return $this->index;
    }

    public function setIndex($index)
    {
        $this->index = $index;
    }

    public function getType()
    {
        return $this->type;
    }

    public function getId()
    {
        return $this->id;
    }
    public function getHit()
    {
        return $this->hit;
    }

    public function getOperate()
    {
        return $this->operate;
    }

    public function toArray()
    {
        return [
            'index'=>$this->index,
            'type'=>$this->type,
            'id'=>$this->id,
            'operate'=>$this->getOperate(),
            'hit'=>$this->hit
        ];
    }
}
<?php
namespace app\model;

use think\facade\Db;
use think\Model;

/**
 * 酒闻评论
 */
class AdminModel extends Model {
    protected $name='admin_log';

    protected function getTableName()
    {
        return Db::name($this->name);
    }
    /**
     * 后台管理--管理员操作日志
     *
     * @param array $id 更新id
     * @return boolean true/false 返回结果
     */
    public function syncData($adminInfo,$params){
        $adminParams = [
            'adminid'=>$adminInfo['id'],
            'adname'=>$adminInfo['username'],
            'ip'=>$params['ip'],
            'tokenval'=>$params['securitycheckval'],
            'status'=>1,
            'title'=>$params['title'],
            'content'=>$this->getLastSql(),
            'addtime'=>time(),
        ];
        return $this->insert($adminParams);
    }

    
}
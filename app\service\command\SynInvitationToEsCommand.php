<?php
declare (strict_types=1);

namespace app\service\command;


use app\model\WeiboModel;
use app\service\elasticsearch\Document;
use app\service\elasticsearch\types\WeiboDocument;
use app\service\ElasticSearchService;
use think\facade\Db;


class SynInvitationToEsCommand
{
    private $weiboModel;
    private $document;
    private $model;

    private function init()
    {
        $this->weiboModel = new WeiboModel();
        $this->document = new Document();
    }

    public function exec($model)
    {
        $this->init();
        $this->model = $model;

        $waitSynData = $this->model->where(['type' => 3])->select()->toArray();
        if(empty($waitSynData)){
            exit;
        }
        $waitSynIdsArr = array_column($waitSynData, 'task_id');
        $reWaitSysData = array_column($waitSynData, null, 'task_id');

        $fields = [
            'a.weibo_id',
            'a.content',
            'a.address',
            'a.wename',
            'a.is_best',
            'a.istop',
            'b.title',
        ];

        $data = $this->weiboModel->alias('a')
            ->field($fields)
            ->leftJoin('weibo_topic b','a.topicids = b.id')
            ->whereIn('a.weibo_id',$waitSynIdsArr)
            ->order(['a.istop'=>'desc','a.ctime'=>'desc'])
            ->select()->toArray();

        foreach ($data as &$item) {
            $item['resource'] = 'weibo';
            $item['effect_time_range'] = ['gte' => 0, 'lte' => 4747881840];
            $item['id'] = $item['weibo_id'];
            $weiboDocument = new WeiboDocument($item['weibo_id']);
            $operate = !empty($reWaitSysData[$item['weibo_id']]) ? $reWaitSysData[$item['weibo_id']]['operate'] : 'create';
            $operate = $this->checkData($item['id'],$operate);
            if($operate === true){
                $this->model->where(['task_id' => $item['id']])->delete();
                continue;
            }
            $weiboDocument->setIndex(env('ES.PREFIX').'weibo_'.env('ES.ENV'));
            $weiboDocument->setOperate($operate);
            $weiboDocument->setHit($item);
            $weiboDocument->toArray();
            $result = $this->document->setDocument($weiboDocument)->$operate();
            $this->delData($result);
        }
    }

    private function delData($result)
    {
        $this->model->where(['task_id' => $result['_id']])->delete();
    }

    public function checkData($id,$operate)
    {
        $searchService = new ElasticSearchService();
        $weibo = 'weibo';
        $input = [
            'index' => [$weibo],
            'match' => [['id' => $id]]
        ];
        $res = $searchService->getDocumentList($input);

        if ($operate == 'update') {// update  delete 先判断数据是否存在，在决定是否入库
            if (empty($res['total']['value'])) {
                $operate = 'create';
            }

        }elseif($operate == 'create'){
            if (!empty($res['total']['value'])) {
                $operate = 'update';
            }

        } elseif ($operate == 'delete') {
            if (empty($res['total']['value'])) {
                return true;
            }
        }
        return $operate;
    }
}

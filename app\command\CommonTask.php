<?php

namespace app\command;

use app\service\CommonTaskService;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;

class CommonTask extends Command
{

    protected $service;
    protected function configure()
    {
        // 指令配置
        // 指令配置
        $this->setName('commontask')
            ->addArgument('name', Argument::OPTIONAL, "模块")
            ->addOption('even',null,Argument::OPTIONAL,'事件')
            ->setDescription('公用任务');
    }

    protected function execute(Input $input, Output $output)
    {
        // 指令输出
        $argument = trim($input->getArgument('name')??"default");
        switch ($argument){
            case 'default':
                $output->writeln('默认方法、选择输入执行命令');
                break;
            case 'article'://批量酒闻训练
                if(!$input->hasOption('even')) $output->writeln('请输入even操作的内容');
                $even = $input->getOption('even');
                switch ($even){
                    case 'embeddings'://训练
                        CommonTaskService::wincNewsEmbeddings();
                        break;

                    default:
                        $output->writeln('没有该选项');
                        break;
                }

                break;
            default:
                $output->writeln('其它操作');
                break;
        }
        // 指令输出
        $output->writeln('commontask');
    }

     

}
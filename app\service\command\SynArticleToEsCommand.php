<?php
declare (strict_types=1);

namespace app\service\command;


use app\Model\ArticleModel;
use app\service\elasticsearch\Document;
use app\service\elasticsearch\types\ArticleDocument;
use app\service\ElasticSearchService;
use think\facade\Db;


class SynArticleToEsCommand
{
    private $articleModel;
    private $document;
    private $model;

    private function init()
    {
        $this->articleModel = new ArticleModel();
        $this->document = new Document();
    }

    public function exec($model)
    {
        $this->init();
        $this->model = $model;

        $waitSynData = $this->model->where(['type' => 2])->select()->toArray();
        if(empty($waitSynData)){
            exit;
        }
        $waitSynIdsArr = array_column($waitSynData, 'task_id');
        $reWaitSysData = array_column($waitSynData, null, 'task_id');

        $fields = [
            'id',
            'title',
            'abst',
        ];

        $data = $this->articleModel->field($fields)
            ->where(['status' => 1])
            ->whereIn('id',$waitSynIdsArr)
            ->order('add_time desc')
            ->select()->toArray();

        foreach ($data as &$item) {
            $item['resource'] = 'article';
            $item['effect_time_range'] = ['gte' => 0, 'lte' => 4747881840];
            $articleDocument = new ArticleDocument($item['id']);
            $operate = !empty($reWaitSysData[$item['id']]) ? $reWaitSysData[$item['id']]['operate'] : 'create';
            $operate = $this->checkData($item['id'],$operate);
            if($operate === true){
                $this->model->where(['task_id' => $item['id']])->delete();
                continue;
            }
            $articleDocument->setIndex(env('ES.PREFIX').'article_'.env('ES.ENV'));
            $articleDocument->setOperate($operate);
            $articleDocument->setHit($item);
            $articleDocument->toArray();
            $result = $this->document->setDocument($articleDocument)->$operate();
            $this->delData($result);
        }
    }

    private function delData($result)
    {
        $this->model->where(['task_id' => $result['_id']])->delete();
    }

    public function checkData($id,$operate)
    {
        $searchService = new ElasticSearchService();
        $article = 'article';
        $input = [
            'index' => [$article],
            'match' => [['id' => $id]]
        ];
        $res = $searchService->getDocumentList($input);

        if ($operate == 'update') {// update  delete 先判断数据是否存在，在决定是否入库
            if (empty($res['total']['value'])) {
                $operate = 'create';
            }

        }elseif($operate == 'create'){
            if (!empty($res['total']['value'])) {
                $operate = 'update';
            }

        } elseif ($operate == 'delete') {
            if (empty($res['total']['value'])) {
                return true;
            }
        }
        return $operate;
    }
}

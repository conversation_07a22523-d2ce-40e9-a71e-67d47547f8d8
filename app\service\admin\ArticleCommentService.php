<?php

namespace app\service\admin;

use app\BaseService;
use app\model\ArticleCommentModel;
use Config;

/**
 * 后台管理--微博评论列表
 */
class ArticleCommentService extends BaseService {
    /**
     * 后台管理--微博评论列表
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function getArticleCommentList($param,$header){           

        //查询数据
        $ArticleCommentModel = new ArticleCommentModel();
        
        $data = $ArticleCommentModel->getArticleCommentList($param);

        //数据处理
        $arr = [];
        if(!empty($data['list'])){  
            $userid = array_column($data['list'],'uid');
            //查询微博对应的用户信息
            $userid = arrayToStr($userid);
            $urlString = '?uid='.$userid.'&field=nickname,avatar_image,uid';
            $userinfo = $this->PubForward($header, $urlString, '/user/v3/profile/getUserInfo', 1,2);
            $nickname = array_column($userinfo['data']['list'],null,'uid');
            foreach($data['list'] as $key => $val){
                $nownickname = isset($nickname[$val['uid']]['nickname'])?$nickname[$val['uid']]['nickname']:'';//当前用户昵称
                $avatar_image = isset($nickname[$val['uid']]['avatar_image'])?$nickname[$val['uid']]['avatar_image']:'';//当前用户头像
                $val['addtime'] = date('Y-m-d H:i:s',$val['addtime']);
                $val['nickname'] = isset($nickname)?$nownickname:'';//昵称
                $val['avatar_image'] = isset($avatar_image)?env('OSS.ALIURL').$avatar_image:'';//头像
                $arr[] = $val;
            }
        }

        $data['list'] = $arr;

        return  $data;
    }


    /**
     * 后台管理--删除酒闻评论
     *
     * @param array $param 提交参数
     * @param array $header 头部参
     * @return array $res 返回结果
     */
    public function delArticleComment($param,$header){        
        if(!isset($param['comid']) || empty($param['comid'])){
            $this->throwError('酒闻类型id不能为空');
        }
        $param['is_show'] = isset($param['is_show'])?$param['is_show']:0;

        $ArticleCommentModel = new ArticleCommentModel();
        $where = explode(',',$param['comid']);//更新条件
        if ($param['is_show']==1){
            //启用时删除敏感用户数据
            $info = $ArticleCommentModel->where('comid','=',$param['comid'])->find();
            $s_data = array();
            $s_data['uid'] = $info['uid']??'';
            $s_data['comment_id'] = (int)$param['comid'];
            $s_data['type'] = ($info['recomid']==0)?1:2;//酒闻评论回复
            //$this->delSensitive($s_data, $header);//暂时没有这个接口
        }
        $ArticleCommentModel->delArticleComment($where,$param['is_show']);
        $res['flag'] = 1;
        $res['msg'] = '操作成功';

        return $res;
    }

    /**
     * 删除敏感用户数据
     * @param $data
     * @param $header
     */
    public function delSensitive($data,$header)
    {
        $url = 'mall/sensitiveuser/delSpecifyData';
        $data['source'] = 5;//酒闻
        $this->PubForward($header, $data, $url, 2);
    }

    public function changeCommentHotVaule($param)
    {
//        dd($param);
        $ArticleCommentModel = new ArticleCommentModel();
        $result = $ArticleCommentModel->where("comid",$param["comid"])->update(["hot_vaule"=>$param["hot_vaule"]]);
        return $result;
    }
}
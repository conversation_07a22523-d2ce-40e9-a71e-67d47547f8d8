<?php
declare (strict_types = 1);

namespace app\validate;

class ArticleValidate extends BaseValidate
{
    /**
     * 定义验证规则
     * 格式：'字段名'	=>	['规则1','规则2'...]
     *
     * @var array
     */
	protected $rule = [
        'title' => 'require',
        'cate_id' => 'require',
        'abst' => 'require|max:2000',
//        'info' => '',  // 改为非必填，保持向后兼容
//        'md_info' => ''  // 新增 md_info 字段，非必填
    ];
    
    /**
     * 定义错误信息
     * 格式：'字段名.规则名'	=>	'错误信息'
     *
     * @var array
     */	
    protected $message = [
        'title.require' => '标题名称不能为空',
        //'title.unique' => '标题名称已存在',
        'cate_id.require' => '所属分类不能为空',
        'abst.require' => '摘要简介不能为空',
        'abst.max' => '摘要简介最多不能超过2000个字符',
        // 移除 info.require 错误信息，因为 info 字段已改为非必填
    ];

    /**
     * 自定义验证方法：确保 info 和 md_info 至少有一个不为空
     */
    public function goCheck($data = '')
    {
        // 先执行父类的基础验证
        $result = parent::goCheck($data);

        if ($result === true) {
            // 基础验证通过后，检查内容字段
            $info = isset($data['info']) ? trim($data['info']) : '';
            $md_info = isset($data['md_info']) ? trim($data['md_info']) : '';

            // 如果两个字段都为空，则抛出错误
            if (empty($info) && empty($md_info)) {
                $this->throwError('文章内容不能为空，请填写详细内容或MD格式内容', '10002', 200);
            }
        }

        return $result;
    }

}
<?php
namespace app\service\es\elasticsearch\searches;

/**
 * bool 查询
 * Class BoolSearch
 * @package App\Services\Elasticsearch
 */

use \app\service\es\elasticsearch\searches\Search as SearchInterface;

class BoolSearch implements SearchInterface
{

    private $index      = [];
    private $terms      = [];
    private $range      = [];
    private $match      = [];
    private $match_phrase  = [];
    private $multiMatch = [];
    private $sort       = [];
    private $page       = 1;
    private $source     = [];
    private $limit      = null;

    public function setInput($input)
    {
        if (empty($input)) {
            return $this;
        }
        if (!empty($input['index'])) {
            $this->index = $input['index'];
        }
        if (!empty($input['terms'])) {
            $this->terms = $input['terms'];
        }
        if (!empty($input['range'])) {
            $this->range = $input['range'];
        }
        if (!empty($input['match'])) {
            $this->match = $input['match'];
        }
        if (!empty($input['match_phrase'])) {
            $this->match_phrase = $input['match_phrase'];
        }
        if (!empty($input['multi_match'])) {
            $this->multiMatch = $input['multi_match'];
        }
        if (!empty($input['source'])) {
            $this->source = $input['source'];
        }
        if (!empty($input['sort'])) {
            $this->sort = $input['sort'];
        }
        if (!empty($input['page'])) {
            $this->page = $input['page'];
        }
        if (!empty($input['limit'])) {
            $this->limit = $input['limit'];
        }
    }
    public function addIndex($index)
    {
        array_push($this->index, $index);
        return $this;
    }

    public function addTerm($term)
    {
        array_push($this->terms, $term);
        return $this;
    }

    public function addRange($range)
    {
        array_push($this->range, $range);
        return $this;
    }

    public function addMatch($match)
    {
        array_push($this->match, $match);
        return $this;
    }

    public function multiMath($multiMatch)
    {
        array_push($this->multiMatch, $multiMatch);
    }

    public function addSort($sort)
    {
        array_push($this->sort, $sort);

        return $this;
    }

    public function setPage($page)
    {
        $this->page = $page;
        return $this;
    }

    public function setLimit($limit)
    {
        $this->limit = $limit;
        return $this;
    }

    public function getIndex()
    {
        return $this->index;
    }

    public function getBody()
    {
        $body = [];
        $query = [
            'bool'   => [
                'filter' => [],
                'must'   => []
            ]
        ];
        if (!empty($this->terms)) {
            $terms = [];
            foreach ($this->terms as $key=>$value) {
                array_push($terms, [
                    'terms' => $value
                ]);
            }
            $query['bool']['filter'] = array_merge($query['bool']['filter'], $terms);
        }
        if (!empty($this->range)) {
            $range = [];

            foreach ($this->range as $key=>$value) {
                array_push($range, [
                    'range'=>$value
                ]);
            }
            $query['bool']['filter'] = array_merge($query['bool']['filter'], $range);
        }
        if (!empty($this->match)) {
            $match = [];

            foreach ($this->match as $key=>$value) {
                array_push($match, [
                    'match' => $value
                ]);
            }

            $query['bool']['must'] = array_merge($query['bool']['must'], $match);
        }
        if (!empty($this->match_phrase)) {
            $match_phrase = [];

            foreach ($this->match_phrase as $key=>$value) {
                array_push($match_phrase, [
                    'match_phrase' => $value
                ]);
            }

            $query['bool']['must'] = array_merge($query['bool']['must'], $match_phrase);

        }
        if (!empty($this->multiMatch)) {
            $multiMatch = [];

            foreach ($this->multiMatch as $key=>$value) {
                array_push($multiMatch, [
                    'multi_match' => $value
                ]);
            }

            $query['bool']['must'] = array_merge($query['bool']['must'], $multiMatch);
        }

        !empty($this->source) && $body['_source'] = $this->source;

        $body['query'] = $query;

        if (!is_null($this->limit)) {
            $body['size'] = $this->limit;
        }
        if (!is_null($this->page) && !is_null($this->limit)) {
            $body['from'] = ($this->page - 1) * $this->limit;
        } else {
            $body['from'] = 0;
        }

        if (!empty($this->sort)) {
            $sort = [];

            foreach ($this->sort as $key=>$value) {

                array_push($sort, $value);
            }

            $body['sort'] = $sort;
        }
        return $body;
    }
}
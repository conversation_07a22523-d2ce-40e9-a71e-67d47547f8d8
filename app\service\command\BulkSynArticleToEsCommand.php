<?php
declare (strict_types=1);

namespace app\service\command;


use app\service\elasticsearch\Document;
use app\service\elasticsearch\types\ArticleDocument;
use app\service\elasticsearch\types\WineAcademyDocument;
use think\facade\Db;


class BulkSynArticleToEsCommand
{
    private $articleModel;
    private $document;

    private function init()
    {
        $this->document = new Document();
    }

    public function exec($model)
    {
        $this->init();
        $this->articleModel = $model;

        $fields = [
            'id',
            'title',
            'abst',
        ];
        $data = $this->articleModel->field($fields)
            ->where(['status' => 1])
            ->where('cstatus != -1')
            ->order('add_time desc')
            ->select()->toArray();
        $documents = [];

        foreach ($data as $item) {
            $item['resource'] = 'article';
            $item['effect_time_range'] = ['gte' => 0, 'lte' => 4747881840];
            $articleDocument = new ArticleDocument($item['id']);
            $operate = !empty($reWaitSysData[$item['id']]) ? $reWaitSysData[$item['id']]['operate'] : 'create';
            $articleDocument->setIndex(env('ES.PREFIX').'article_'.env('ES.ENV'));
            $articleDocument->setOperate($operate);
            $articleDocument->setHit($item);
            $articleDocument->toArray();
            $documents[] = $articleDocument;
        }
        $this->document->bulk($documents);
    }
}

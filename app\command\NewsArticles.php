<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;

class NewsArticles extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('newsaeticles')
            ->addArgument('entity', Argument::OPTIONAL, "your name")//实体
            ->addOption('event', null, Option::VALUE_REQUIRED, 'city name')//动作
            ->setDescription('酒闻文章管理命令');
    }

    protected function execute(Input $input, Output $output)
    {

        $entity =$input->getArgument('entity');//实体
        $eventhas = $input->hasOption('event');//动作
        $event = $input->getOption('event');

        if($eventhas){
            if($entity == "comment" && $event == 'numupd'){//评论数据同步
                $output->writeln("执行更行数据评论数");


            }
        }else{
            $output->writeln("没用动作");
            return ;
        }

        // 指令输出
//        $output->writeln('aeticlecommentcommand'.$entity.$event);
    }
}

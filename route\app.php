<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

Route::get('/', function () {
    return 'ok-tp6-news';
});

Route::get('hello/:name', 'index/hello');

//后台
Route::group('news/v3', function () {
    /**酒闻管理前端**/
    Route::get('/articleClient/getArticleList', 'ArticleController/getIndexArticleList');//酒闻资讯--酒闻列表
    Route::get('/articleClient/getArticleDetails', 'ArticleController/getIndexArticleDetails');//酒闻资讯--酒闻详情
    Route::get('/articleClient/articleCreateJs', 'ArticleController/getArticleCreateJs');//酒闻资讯--详情生成js
    Route::get('/articleClient/getArticleShareUrl', 'ArticleController/getIndexArticleShareUrl');//酒闻资讯--资讯分享
    Route::get('/articleClient/getArticleComment', 'ArticleController/getIndexArticleComment');//酒闻资讯--资讯评论列表
    Route::post('/articleClient/doDigg', 'ArticleController/doIndexDigg');//酒闻资讯--资讯、评论点赞
    Route::post('/articleClient/cancelLike', 'ArticleController/cancelLike');//酒闻资讯--资讯、评论取消点赞
    Route::post('/articleClient/getArticleCollect', 'ArticleController/getIndexArticleCollect');//酒闻资讯--资讯收藏
    Route::post('/articleClient/MakeCommentOn', 'ArticleController/MakeCommentOn');//酒闻资讯--发表评论
    Route::get('/articleClient/getMyArticleCollectList', 'ArticleController/getIndexMyArticleCollectList');//酒闻资讯--酒闻收藏列表
    Route::get('/articleClient/getMyArticleCollectCount', 'ArticleController/getMyArticleCollectCounts');//酒闻资讯--酒闻收藏列表
    Route::post('/articleClient/getIsCollect', 'ArticleController/getIsCollect');//酒闻资讯--用户是否收藏
    /** 酒闻管理 */
    Route::get('/article/getArticleList', 'ArticleController/getArticleList');//酒闻列表
    Route::post('/article/operateArticle', 'ArticleController/operateArticle');//添加酒闻
    Route::post('/article/changeArticleStatus', 'ArticleController/changeArticleStatus');//更改酒闻状态
    Route::get('/article/getArticleInfo', 'ArticleController/getArticleInfo');//酒闻详情
    Route::post('/article/delArticle', 'ArticleController/delArticle');//删除酒闻
    Route::post('/article/articleSort', 'ArticleController/articleSort');//排序

    /** 酒闻评论管理 */
    Route::get('/articlecom/getArticleCommentList','ArticleCommentController/getArticleCommentList');//酒闻评论列表
    Route::post('/articlecom/delArticleComment','ArticleCommentController/delArticleComment');//删除酒闻评论
    Route::post('/articlecom/MakeCommentOn', 'ArticleCommentController/MakeCommentOn');//酒闻资讯,发表评论不验证敏感词,审核
    Route::post('/articlecom/changeCommentStatus', 'ArticleCommentController/changeCommentStatus');//更酒闻评论审核状态
    Route::post('/articlecom/changeCommentHotVaule', 'ArticleCommentController/changeCommentHotVaule');//更酒闻评论热度值

    /** 酒闻类型管理 */
    Route::get('/articletype/getArticleTypeList', 'ArticleTypeController/getArticleTypeList');//酒闻类型列表
    Route::get('/articletype/getArticleType', 'ArticleTypeController/getArticleType');//酒闻类型
    Route::get('/articletype/getArticleTypeDetails', 'ArticleTypeController/getArticleTypeDetails');//酒闻类型详情
    Route::post('/articletype/operateArticleType', 'ArticleTypeController/operateArticleType');//添加/编辑酒闻类型
    Route::post('/articletype/changeArticleTypeStatus', 'ArticleTypeController/changeArticleTypeStatus');//更改酒闻类型状态
    Route::post('/articletype/delArticleType', 'ArticleTypeController/delArticleType');//删除酒闻类型
    Route::post('/articletype/articleTypeSort', 'ArticleTypeController/articleTypeSort');//排序

    /** 文章投票功能 - 前端接口 */
    Route::get('/articleClient/getArticleVotes', 'ArticleController/getArticleVotes');//获取文章投票列表
    Route::post('/articleClient/userVote', 'ArticleController/userVote');//用户投票
    Route::get('/articleClient/getVoteResult', 'ArticleController/getVoteResult');//获取投票结果
    Route::get('/articleClient/getUserVoteStatus', 'ArticleController/getUserVoteStatus');//检查用户投票状态
    Route::get('/articleClient/getUserVoteHistory', 'ArticleController/getUserVoteHistory');//获取用户投票历史

    /** 文章投票功能 - 后台管理接口 */
    Route::get('/articlevote/getVoteList', 'ArticleController/getVoteList');//获取投票列表
    Route::get('/articlevote/getVoteDetails', 'ArticleController/getVoteDetails');//获取投票详情
    Route::post('/articlevote/createVote', 'ArticleController/createVote');//创建投票
    Route::post('/articlevote/updateVote', 'ArticleController/updateVote');//更新投票
    Route::post('/articlevote/deleteVote', 'ArticleController/deleteVote');//删除投票
    Route::post('/articlevote/batchDeleteVotes', 'ArticleController/batchDeleteVotes');//批量删除投票
    Route::get('/articlevote/getVoteStatistics', 'ArticleController/getVoteStatistics');//获取投票统计
    Route::get('/articlevote/getVoteRecords', 'ArticleController/getVoteRecords');//获取投票记录列表

    Route::post('/DataSyncReceive', 'ArticleDataSyncReceiveController/handleData');//队列数据处理
    Route::post('/DataSyncPool', 'ArticleDataSyncReceiveController/DataSyncPool');//推荐池数据回调
});
<?php
namespace app\service\es\elasticsearch;

use Elasticsearch\ClientBuilder;

/**
 * es 客户端
 * Class Client
 * @package App\Services\Elasticsearch
 */


class Client
{

    public static function getInstance()
    {
        $hosts = [
            [
            'host' => env('ES.HOST','127.0.0.1'),
            'port' => env('ES.PORT',9200),
            'user' => env('ES.USER','root'),
            'pass' => env('ES.PASS','vinehoo666')
           ]
        ];
        $client = ClientBuilder::create()->setHosts($hosts)->build();
        return $client;
    }
}
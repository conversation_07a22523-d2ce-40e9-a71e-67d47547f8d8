<?php


namespace app\service\command;


use app\service\admin\ArticleService;
use think\facade\Db;

class CreateArticlesJsCommand
{
    public function exec()
    {
        $where []= ['status','=',1];
        $where []= ['cstatus','<>',-1];

        $result=[];
        Db::name("article")->where($where)->chunk(100,function ($res){
            foreach ($res as $v){
                $res= (new ArticleService())->createJson($v['id']);
                if ($res  && isset($res['error_code']) && $res['error_code'] == 0){
                    $result[]=$v['id'];
                    echo $v['id']."\n";
                }
            }
        });

        return $result;
    }

}
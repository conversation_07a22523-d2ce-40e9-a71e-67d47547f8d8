<?php
namespace app\service\intelligent;
use app\model\WeiboModel;
use think\facade\Db;

/**
 * Class ArticleContent
 * @package App\Services\intelligent
 */


class ArticleContent extends Content
{
    private $ids = [];
    private $type = 'article';
    private $input = [];
    public function getType()
    {
        return $this->type;
    }

    public function addId($id)
    {
        array_push($this->ids, $id);
    }

    public function setInput($input)
    {
        array_push($this->input, $input);
    }

    //返回数据
    public function getList()
    {
        if (empty($this->input)) {
            return [];
        }

        $fields = [
            'id',
            'viewnums',
            'commentnums',
            'img',
        ];

        $dataExt = Db::name('article')
            ->field($fields)
            ->whereIn('id', $this->ids)
            ->select()->toArray();

        $dataExt = array_column($dataExt, null, 'id');
        $data = $this->input;

        foreach ($data as &$item){
            if (empty($dataExt[$item['id']])) {
                continue;
            }
            $item['img'] = env('OSS.ALIURL').'/'.$dataExt[$item['id']]['img'];
            $item['viewnums'] = $dataExt[$item['id']]['viewnums'];
            $item['commentnums'] = $dataExt[$item['id']]['commentnums'];
            unset($item['effect_time_range']);
        }
        return $data;
    }
}
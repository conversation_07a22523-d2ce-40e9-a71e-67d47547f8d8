<?php
namespace app\controller;

use app\BaseController;
use app\Request;
use app\service\admin\ArticleCommentService;
use app\service\admin\ArticleService;
use think\facade\Validate;

/**
 * 后台管理--酒闻评论管理
 */
class ArticleCommentController extends BaseController
{
    /**
    * @OA\Post(path="/lunjiu/admin/articlecom/getArticleCommentList",
    *   tags={"酒闻评论后台管理"},
    *   summary="酒闻评论列表",
    *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
    *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
    *   @OA\RequestBody(
    *     @OA\MediaType(
    *       mediaType="multipart/form-data",
    *         @OA\Schema(
    *               @OA\Property(description="资讯ID", property="aid", type="string", default=""),   
    *               @OA\Property(description="关键字", property="keywords", type="string", default=""),    
    *               @OA\Property(description="每页显示条数（不能超过30条）", property="pagesize", type="integer", default="10"),
    *               @OA\Property(description="页码", property="pagenumber", type="integer", default="1"),        
    *           )
    *       )
    *     ), 
    *    @OA\Response(
    *            response=200,
    *            description="接口请求成功",
    *            @OA\MediaType(
    *                mediaType="application/json",
    *            @OA\Schema(
    *                 @OA\Property(property="status",
    *                    type="string",
    *                    example="success/fail",
    *                    description="状态"
    *                ),
    *                 @OA\Property(property="errorCode",
    *                    type="string",
    *                    example="0/-1",
    *                    description="状态编码"
    *                ),
    *                 @OA\Property(property="msg",
    *                    type="string",
    *                    example="ok",
    *                    description="状态描述"
    *                ),
    *                @OA\Property(property="data",
    *                   type="object",description="数据",
    *                   @OA\Property(property="list",type="array",description="酒闻评论数据",
    *                       @OA\Items(
    *                           type="object",description="",
    *                           @OA\Property(property="comid",type="integer",example="1",description="评论id"),
    *                           @OA\Property(property="aid",type="integer",example="1",description="资讯ID"),
    *                           @OA\Property(property="content",type="string",example="毛毛虫",description="评论内容"),
    *                           @OA\Property(property="nickname",type="string",example="毛毛虫化蝶传记",description="回复人"),
    *                           @OA\Property(property="addtime",type="string",example="2020-02-22 22:11:00",description="回复时间"),
    *                       )
    *                   ),
    *                   @OA\Property(property="totalPage",type="string",example="20",description="总页数"),
    *                   @OA\Property(property="nowPage",type="integer",example="1",description="当前页"),
    *                   @OA\Property(property="totalNum",type="integer",example="50",description="数据总条数"),
    *                   @OA\Property(property="pagesize",type="integer",example="10",description="每页显示条数"),
    *               ),
    *            )
    *        ),
    *    )
    * )
    */
    public function getArticleCommentList(Request $request){
        $param = $request->param();
        $header = $request->header();
        //$is_true = isset($header['securitycheckval']);//判断是否存在token参数
        //$header['securitycheckval'] = $is_true?$header['securitycheckval']:'';//token值
        $ArticleCommentService = new ArticleCommentService();
        $is_res = $ArticleCommentService->getArticleCommentList($param,$header);     
        
        return $this->success($is_res);
    }


    /**
    * @OA\Post(path="/lunjiu/admin/articlecom/delArticleComment",
    *   tags={"酒闻评论后台管理"},
    *   summary="删除酒闻评论",
    *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
    *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
    *   @OA\RequestBody(
    *     @OA\MediaType(
    *       mediaType="multipart/form-data",
    *         @OA\Schema(
    *               @OA\Property(description="评论id（批量删除是id为英文逗号分隔的字符串）", property="comid", type="string", default=""),
    *               @OA\Property(description="品论需要改变到的状态", property="is_show", type="string", default="1"),
    *           required={"comid"})
    *       )
    *     ), 
    *    @OA\Response(
    *            response=200,
    *            description="接口请求成功",
    *            @OA\MediaType(
    *                mediaType="application/json",
    *            @OA\Schema(
    *                 @OA\Property(property="status",
    *                    type="string",
    *                    example="success/fail",
    *                    description="状态"
    *                ),
    *                 @OA\Property(property="errorCode",
    *                    type="string",
    *                    example="0/-1",
    *                    description="状态编码"
    *                ),
    *                 @OA\Property(property="msg",
    *                    type="string",
    *                    example="ok",
    *                    description="状态描述"
    *                ),
    *                @OA\Property(property="data",
    *                   type="object",description="数据",
    *               ),
    *            )
    *        ),
    *    )
    * )
    */
    public function delArticleComment(Request $request){
        $param = $request->param();
        $header = $request->header();
        $is_true = isset($header['securitycheckval']);//判断是否存在token参数
        $header['securitycheckval'] = $is_true?$header['securitycheckval']:'';//token值
        $ArticleCommentService = new ArticleCommentService();
        $is_res = $ArticleCommentService->delArticleComment($param,$header); 
        
        if($is_res['flag'] == 1){
            return $this->success($is_res['msg']);
        }else{
            return $this->failed($is_res['msg'],$is_res['flag']);
        }
    }

    public function MakeCommentOn(Request $request){
        $param = $request->param();
        $header = $request->header();
        $ArticleService = new ArticleService();
        $is_res = $ArticleService->MakeCommentOn($param,$header,1);

        if($is_res['flag'] == 1){
            return $this->success();//['list'=>$is_res['data']]
        }else{
            return $this->failed($is_res['msg'],$is_res['flag']);
        }
    }

    public function changeCommentStatus(Request $request){
        $param = $request->param();
        $validate = Validate::rule([
            'id|评论id'=>'require|number',
            'status|更改状态'=>'require|in:1,2,3,4',
        ]);
        if (!$validate->check($param)) {
            return $this->failed($validate->getError());

        }
        $ArticleService = new ArticleService();
        $is_res = $ArticleService->changeCommentStatus($param);
        if($is_res['flag'] == 1){
            return $this->success(['list'=>$is_res['data']]);
        }else{
            return $this->failed($is_res['msg'],$is_res['flag']);
        }
    }

    public function changeCommentHotVaule(Request $request)
    {
        $param = $request->param();
        $validate = Validate::rule([
            'comid|评论id'=>'require|number',
            'hot_vaule|热度值'=>'require|number',
        ]);
        if (!$validate->check($param)) {
            return $this->failed($validate->getError());

        }

        $ArticleService = new ArticleCommentService();
        $is_res = $ArticleService->changeCommentHotVaule($param);
        if (isset($is_res)){
            $this->msg = "修改完成,受影响行数：".$is_res;
            return $this->success([]);
        }
    }

}
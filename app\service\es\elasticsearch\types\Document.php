<?php
namespace app\service\es\elasticsearch\types;

use \app\service\es\elasticsearch\Document as SDocument;
/**
 * 创建es mapping
 * Class Document
 * @package App\Services\Elasticsearch
 */

class Document
{
    protected $index;
    protected $type  = '_doc';
    protected $id;
    protected $hit;
    protected $operate;
    
    public function __construct($index = 'user')
    {
        $this->index = env('ES.PREFIX').$index;
        return $this;
    }

    public function sendOperate($hit, $operate = 'create')
    {
        $this->id      = $hit['uid'];
        $this->operate = $operate;
        $this->hit     = $hit;

        return $this->sendDocument();
    }

    public function getIndex()
    {
        return $this->index;
    }

    public function setIndex($index)
    {
        $this->index = $index;
    }

    public function getType()
    {
        return $this->type;
    }

    public function getId()
    {
        return $this->id;
    }
    public function getHit()
    {
        return $this->hit;
    }

    public function getOperate()
    {
        return $this->operate;
    }

    public function sendDocument()
    {
        return (new SDocument())->setDocument($this);
    }

    public function toArray()
    {
        return [
            'index'=>$this->index,
            'type'=>$this->type,
            'id'=>$this->id,
            'operate'=>$this->getOperate(),
            'hit'=>$this->hit
        ];
    }
}
<?php

namespace app\service;

use app\BaseService;
use app\model\ArticleModel;
use Config;
use think\facade\Db;

/**
 * 酒闻资讯
 */
class ArticleService extends BaseService {
    /**
     * 酒闻列表查询
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function getArticleList($param,$header){

        $uid = $header['vinehoo-uid']??"";
        $client = $header['vinehoo-client']??"";
        if($param['limit'] > 30){
            $this->throwError('分页数量最大不能超过30条');
        }
        
        //查询数据
        $ArticleModel = new ArticleModel();
        $data = $ArticleModel->getArticleList($param);

        //数据处理
        if(!empty($data['list'])){            
            foreach($data['list'] as $key => $val){

                if($client != "vinehoo-pc"){
                    $extstr = strstr($val['img'],"?",true)?:$val['img'];
                    $w =288; $h=288;$sjf = strpos($val['img'],'?')?"&":"?";
                    $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                    //文章缩略图图片处理
                    $data['list'][$key]['img'] = env('OSS.ALIURL').$val['img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                }else{
                    $data['list'][$key]['img'] = env('OSS.ALIURL').$val['img'];
                }

                if($uid != ""){//当前用户的为通过评论数累加
                    $where = [
                        ['aid','=',$val['id']],
                        ['uid','=',$uid],
                        ['is_show','=',0],
                        ['audit_status','<>',3]
                    ];//非被驳回的条件
                    $nums = 0;
//                    if($val['id'] == 9950) $nums = $this->getCommentnums($val['id'],$uid);
                    $nums = $this->getCommentnums($val['id'],$uid);
//                    $count = Db::name('article_comment')->where($where)->count();//数量
//                    $data['list'][$key]['commentnums'] = $data['list'][$key]['commentnums']+$count;
                    $data['list'][$key]['commentnums'] = $nums;
                }
            }
        }


        $msg['flag'] = 1;
        $msg['msg'] = '查询成功';
        $msg['data'] = $data;

        return $msg;
    }


    /**
     * 返回新闻评论数量
     * @param $newid
     * @param $uid
     * @return int
     */
    public function getCommentnums($newid,$uid)
    {

        $nums = 0;
        $boxarr = [];//块
        $boxtop = [];//第一个块的顶级
        //boxid 块ID
        $result = Db::name('article_comment')->field("comid,uid,reuid,boxid,audit_status,recomid")->where(['aid'=>$newid])->select();//->column("reuid",null);//数量
        if(!$result) return $nums;

        //获取块级评论数组
        foreach ($result as $v){
            $boxarr[$v['boxid']][] = $v;
            if ($v['recomid'] == 0 ) array_push($boxtop,$v);
        }

        foreach ($boxtop as $v){//一级评论下的数量统计
            $unitarr = $boxarr[$v['boxid']];//对应块级数据
            if(($v['audit_status'] == 1 ||$v['audit_status'] == 4)){
                foreach ($unitarr as $val){
                    if(($val['uid'] == $uid && $val['audit_status'] != 3)) $nums +=1;
                }
            }else if($v['audit_status'] == 2){
                foreach ($unitarr as $val){
                    if(($val['audit_status']==2)||($val['uid'] == $uid && $val['audit_status'] != 3)) $nums +=1;
                }
            }

        }
        return $nums;
    }

    /**
     * 酒闻详情查询
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function getArticleDetails($param,$header){
        $securitycheckval = $header['securitycheckval'];//token值

        //验证token，token过期重新授权登录(未登陆可以不验证token)
        if($securitycheckval || $securitycheckval != ''){
            $checkInfo = $this->checkRedisToken($securitycheckval);//验证
            $userinfo = $checkInfo['data'];
            //查询用户信息
            //$myuserinfo = $this->getUidAllInfo($header, $userinfo['uid'], $param['uuid']); 
        }               
        
        if(empty($param['id'])){
            $this->throwError('资讯id不能为空');
        }
        
        //查询数据
        $ArticleModel = new ArticleModel();
        $data = $ArticleModel->getArticleDetails($param['id']);

        //更新浏览量
        $ArticleModel->updateArticleInfo($param['id'],'viewnums',1);

        //查询文章是否收藏
        if(isset($userinfo['uid'])){
            $is_collect = $ArticleModel->getCollectInfo($param['id'],$userinfo['uid']);
        }else{
            $is_collect = 0;
        }

        if(empty($data)){
            $data = [];
        }

        $data['img'] = env('OSS.ALIURL').$data['img'];

        if (!empty($data['md_info'])) {
            $data['info_field'] = 'md_info';
        } else {
            $data['info_field'] = 'info';
        }

        $data['is_collect'] = $is_collect?1:0;//是否收藏 1已收藏 0收藏

        //分享地址
        $data['share_link'] = Config('config')['WEIBO_SHARE_URL']."/web-static/details/wineNewsDetail.html?id={$param['id']}";

        //酒闻详情地址
        $data['detail_link'] = Config('config')['WEIBO_SHARE_URL']."/h5/wineNewsDetail/{$param['id']}";

        $msg['flag'] = 1;
        $msg['msg'] = '查询成功';
        $msg['data'] = $data;

        return $msg;
    }
    
    
    /**
     * 酒闻收藏
     *
     * @param array $param 提交参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function getArticleCollect($param,$header){
        $securitycheckval = $header['securitycheckval'];//token值

        //验证token，token过期重新授权登录(未登陆可以不验证token)
        $checkInfo = $this->checkRedisToken($securitycheckval);//验证
        $userinfo = $checkInfo['data'];   
        //查询用户信息
        //$myuserinfo = $this->getUidAllInfo($header, $userinfo['uid'], $param['uuid']);
        $myuserinfo = $userinfo['loginname'];//电话号码
        if(empty($myuserinfo)){
            $this->throwError('请绑定手机');
        }         
        
        if(empty($param['id'])){
            $this->throwError('资讯id不能为空');
        }
        
        //查询数据
        $ArticleModel = new ArticleModel();
        $data = $ArticleModel->getArticleDetails($param['id']);
        if(empty($data)){
            $this->throwError('该资讯不存在');
        }

        if($param['status'] == 1){//类型 0取消收藏 1收藏
            //查询文章是否收藏
            $is_collect = $ArticleModel->getCollectInfo($param['id'],$userinfo['uid']);
            if(!empty($is_collect)){
                $this->throwError('该资讯已收藏');
            }

            //添加收藏
            $is_res  = $ArticleModel->AddCollectArticle($param['id'],$userinfo['uid']);

            if($is_res){
                $msg['flag'] = 1;
                $msg['msg'] = '收藏成功';
            }else{
                $msg['flag'] = 0;
                $msg['msg'] = '收藏失败';
            }
        }else{
            //取消收藏
            $is_res  = $ArticleModel->delCollectArticle($param['id'],$userinfo['uid']);
            if($is_res === false){
                $msg['flag'] = 0;
                $msg['msg'] = '取消收藏失败';
            }else{
                $msg['flag'] = 1;
                $msg['msg'] = '取消收藏成功';
            }
        }

        return $msg;
    }


    
    /**
     * 资讯评论列表
     *
     * @param array $param 查询使用参数
     * @param array $header 头部参数
     * @return array $msg 查询结果
     */
    public function getArticleComment($param,$header){
        $securitycheckval = $header['securitycheckval'];//token值

        //验证token，token过期重新授权登录(未登陆可以不验证token)
        if($securitycheckval || $securitycheckval != ''){
            $checkInfo = $this->checkRedisToken($securitycheckval);//验证
            $userinfo = $checkInfo['data'];   
            //查询用户信息
            //$myuserinfo = $this->getUidAllInfo($header, $userinfo['uid'], $param['uuid']);
            $myuserinfo = $userinfo['loginname'];//电话号码
            $param['uid'] = $userinfo['uid'];
        }        

        if($param['limit'] > 30){
            $this->throwError('分页数量最大不能超过30条');
        }
        
        //数据处理
        $ArticleModel = new ArticleModel();
        $articlecommentInfo = $ArticleModel->getArticleComment($param);//查询资讯评论 
        $commentInfo = $articlecommentInfo['list'];
        
        $data = [];
        if(!empty($commentInfo)){
            $commentidarr = array_column($commentInfo,'comid');//获取资讯评论id
            $oneuidarr = array_column($commentInfo,'uid');//获取资讯评论用户id
            $boxidarr = array_column($commentInfo,'boxid');//获取资讯评论分块id            

            //获取点赞信息       
            if(!empty($myuserinfo)){
                $commentidstr = implode(',',$commentidarr);
                $wherestr = "articleid={$param['id']} and comid in ($commentidstr) and uid={$userinfo['uid']}";//查询条件
                $diggInfo = $ArticleModel->getBatchDigg($wherestr);                
                $diggarr = array_column((array)$diggInfo, null, 'comid');
            }
            
            //查询帖子评论的用户信息   
            $oneuserinfoarr =  $this->getUserInfoArr($header, $oneuidarr, $param['uuid']);
            $oneuserinfoarrnew = array_column($oneuserinfoarr, null, 'uid');

            //查询帖子评论的回复评论
            $replycommenInfo = $ArticleModel->getArticleReplyComment($boxidarr);
            
            //获取回复评论用户id
            $twouidarr = array_column($replycommenInfo,'uid');
            //查询回复评论的用户信息   
            $twouserinfoarr =  $this->getUserInfoArr($header, $twouidarr, $param['uuid']); 
            $twouserinfoarrnew = array_column($twouserinfoarr, null, 'uid');

            //获取回复评论用户id
            $threeuidarr = array_column($replycommenInfo,'reuid');
            //查询被回复评论的用户信息
            $threeuserinfoarr =  $this->getUserInfoArr($header, $threeuidarr, $param['uuid']); 
            $threeuserinfoarrnew = array_column($threeuserinfoarr, null, 'uid');
            
            foreach($commentInfo as $ckey => $cval){//帖子评论数据
                $cval['is_digg'] = !isset($diggarr[$cval['comid']]['comid'])?1:0;//是否可以点赞 1是 0否

                //评论用户信息
                $cval['userinfo'] = isset($oneuserinfoarrnew[$cval['uid']])?
                [
                    'nickname'=>$oneuserinfoarrnew[$cval['uid']]['nickname'],
                    'avatar_image'=>$oneuserinfoarrnew[$cval['uid']]['avatar_image'],
                    'ulevel'=>$oneuserinfoarrnew[$cval['uid']]['user_level'],
                    'certification_level'=>$oneuserinfoarrnew[$cval['uid']]['certified_info'],
                ]
                :(object)[]; 
                   

                //回复评论数据处理
                foreach($replycommenInfo as $rkey => $rval){  
                    $userinfo = (array)$cval['userinfo'];                   
                                      
                    $rval['article_username'] =  isset($threeuserinfoarrnew[$rval['reuid']])?$threeuserinfoarrnew[$rval['reuid']]['nickname']:'';//被评论的评论用户昵称
                    
                    //评论回复用户信息处理
                    $rval['userinfo'] = isset($twouserinfoarrnew[$rval['uid']])?
                    [
                        'nickname'=>$twouserinfoarrnew[$rval['uid']]['nickname'],
                        'avatar_image'=>$twouserinfoarrnew[$rval['uid']]['avatar_image'],
                        'ulevel'=>$twouserinfoarrnew[$rval['uid']]['user_level'],
                        'certification_level'=>$twouserinfoarrnew[$rval['uid']]['certified_info'],
                    ]
                    :(object)[];
                   
                    if($cval['boxid'] == $rval['boxid']){ 
                        $cval['replyCommentInfo'][] = $rval;
                    }
                     
                }     
                
                $data[] = $cval;
            }
        }      
        $articlecommentInfo['list'] = $data;        
        
        $msg['flag'] = 1;
        $msg['msg'] = '查询成功';
        $msg['data'] = $articlecommentInfo;

        return $msg;
    }


    /**
     *资讯评论点赞 
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function doDigg($param,$header)
    {   
        $securitycheckval = $header['securitycheckval'];//token值

        //验证token，token过期重新授权登录(未登陆可以不验证token)
        $checkInfo = $this->checkRedisToken($securitycheckval);//验证
        $userinfo = $checkInfo['data'];
        //查询用户信息
        //$myuserinfo = $this->getUidAllInfo($header, $userinfo['uid'], $param['uuid']);
        $myuserinfo = $userinfo['loginname'];//电话号码
        if(empty($myuserinfo)){
            $this->throwError('请绑定手机');
        }   
        
        $ArticleModel = new ArticleModel();
        if($param['type'] == 1){//资讯点赞日志
            //查询资讯
            $data = $ArticleModel->getArticleDetails($param['id']);
            if(empty($data)){
                $this->throwError('该资讯不存在');
            }

            $is_digg = $ArticleModel->getArticleLog($param['id'],$userinfo['uid']);
            if(!empty($is_digg)){
                $this->throwError('已点过赞');
            }

            //资讯点赞日志数据
            $log = array(
                'aid' => $param['id'],//文章id
                'uid' => $userinfo['uid'],//用户id
                'versions' => $header['api-version'],//版本号
                'ip' => $_SERVER['SERVER_ADDR'],//ip地址
                'addtime' => date('Y-m-d H:i:s',time())//点赞时间
            );
            $is_res = $ArticleModel->InsertArticleLog($log);
        }else if($param['type'] == 2){//评论点赞日志
            //查询资讯
            $data = $ArticleModel->getComment($param['id']);
            if(empty($data)){
                $this->throwError('该评论不存在');
            }

            $is_digg = $ArticleModel->getCommentLog($param['id'],$userinfo['uid']);
            if(!empty($is_digg)){
                $this->throwError('已点过赞');
            }

            //评论点赞日志数据
            $log = array(
                'articleid' => $data['aid'],//文章id
                'comid' => $param['id'],//评论id
                'uid' => $userinfo['uid'],//用户id
                'addtime' => date('Y-m-d H:i:s',time())//点赞时间
            );
            $is_res = $ArticleModel->InsertCommentLog($log);
        }        

        if($is_res){
            $msg['flag'] = 1;
            $msg['msg'] = '点赞成功';
        }else{
            $msg['flag'] = 0;
            $msg['msg'] = '已点过赞';
        }

        return $msg;
        
    }


    /**
     * 酒闻收藏列表查询
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function geMytArticleCollectList($param,$header){
        $securitycheckval = $header['securitycheckval'];//token值

        //验证token，token过期重新授权登录(未登陆可以不验证token)
        $checkInfo = $this->checkRedisToken($securitycheckval);//验证
        $userinfo = $checkInfo['data'];
        //查询用户信息
        //$myuserinfo = $this->getUidAllInfo($header, $userinfo['uid'], $param['uuid']);
        $myuserinfo = $userinfo['loginname'];//电话号码
        if(empty($myuserinfo)){
            $this->throwError('请绑定手机');
        }   
        
        if($param['limit'] > 30){
            $this->throwError('分页数量最大不能超过30条');
        }
        
        //查询数据
        $ArticleModel = new ArticleModel();
        $data = $ArticleModel->geMytArticleCollectList($param,$userinfo['uid']);

        //数据处理
        $arr = [];
        if(!empty($data['list'])){            
            foreach($data['list'] as $key => $val){
                //文章缩略图图片处理
                $val['img'] = env('OSS.ALIURL').$val['img'];
                $arr[] = $val;
            }
        }

        $data['list'] = $arr;
        
        $msg['flag'] = 1;
        $msg['msg'] = '查询成功';
        $msg['data'] = $data;

        return $msg;
    }


    /**
     * 微博发表评论
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function MakeCommentOn($param,$header){
        $securitycheckval = $header['securitycheckval'];//token值

        //验证token，token过期重新授权登录(未登陆可以不验证token)
        if($securitycheckval || $securitycheckval != ''){
            $checkInfo = $this->checkRedisToken($securitycheckval);//验证
            $userinfo = $checkInfo['data'];   
            //查询用户信息
            //$myuserinfo = $this->getUidAllInfo($header, $userinfo['uid'], $param['uuid']);
            $myuserinfo = $userinfo['loginname'];//电话号码
            if(empty($myuserinfo)){
                $this->throwError('请绑定手机');
            }         
        }else{
            $this->throwError('请先登陆');
        }

        $ArticleModel = new ArticleModel();
        
        $uid = $userinfo['uid'];//用户id
		$aid = $param['id'];//帖子id
        //敏感词判断
        $word_state = $this->filterSensitiveWords($param['content']);
		$content = $param['content'];//评论内容
		$address = isset($param['address'])?$param['address']:'';//地址
        $reuid = isset($param['reuid'])?$param['reuid']:'';//被回复评论的用户id
        $recontent = isset($param['recontent'])?$param['recontent']:'';//被回复的评论内容
        $recomid = isset($param['recomid'])?$param['recomid']:'';//被回复的评论id(未传入表示第一条评论)
        
        //验证用户是否被禁言
        $WeiboService = new WeiboService();
        $userallinfo = $WeiboService->verfiyillegal($header,$uid,$param['uuid']);

        //查询用户限制 1禁止评论 4可见所有评论 5酒款预购黑名单 6社区评论只有自己可见
		$limits = $userallinfo['limits'];
		if(!empty($limits)){
			$is_status = 0;//状态 0 禁用（社区评论只有自己可见） 1启用
		}else{
			$is_status = 1; //状态 0 禁用（社区评论只有自己可见） 1启用
        }

        //微博评论关键词屏蔽
//        $weibo_sensitive_words = $WeiboService->weibo_sensitive_words($header,$param['uuid']);
//
//		$weibo_sensitive_words = explode('|', $weibo_sensitive_words);
//		foreach ($weibo_sensitive_words as $k => $v) {
//			if(strpos($content,$v) !== false){
//                $is_status = 0; //状态 0 禁用（社区评论只有自己可见） 1启用
//			}else{
//                $is_status = 1; //状态 0 禁用（社区评论只有自己可见） 1启用
//            }
//        }

        $save=array();//评论入库信息        
        //评论图片信息处理
		$save['uid']=$uid;//评论用户id		
		$save['recomid']= $recomid?$recomid:0 ;//被回复的评论id
		$save['reuid']=$reuid;//被回复评论的用户id
		$save['aid']=$aid;//文章id
		$save['content']=$content;//评论内容
		$save['address']=empty($address)?"":$address;//评论地址
		$save['recontent']=empty($recontent)?"":$recontent;//回复内容
		$save['addtime']=time();//评论时间
        #敏感词
        if($word_state == true) $save['is_show'] = 0;

        $pubuserinfo = [];

        //判断是评论文章还是回复评论
        if(empty($recomid)){//被回复的评论id如果为空则是评论帖子
            if(strlen($content)>65000) $this->throwError('评论的内容太长！');
            //以评论文章的用户为单位模块
            $temp = array();
            $temp['weibo_id']=$aid;//文章id
            $temp['uid']=$uid;//第一个评论用户id
            $temp['lastuid']=$uid;//最后一个评论用户id
            $temp['lastcontent']=$content;//最后一条评论内容
            $temp['address']=$address;//最后一条评论地址
            $temp['lasttime']=time();//最后一条评论时间
            $temp['type']=1;//评论类型 0微博1资讯
            //添加到评论分块表
            $is_res = $ArticleModel->addBox($temp,$save);           
        }else{//不为空，则是回复评论
            if(strlen($content)>65000) $this->throwError('回复的内容太长！');
            //查询评论用户
            $pubUid = $ArticleModel->getIdComment($recomid);
            $pubuserinfo = $this->getUidAllInfo($header, $pubUid['uid'], $param['uuid']);
            
            $temp = array();
            $temp['lastuid']=$uid;//最后一个评论用户id
            $temp['lastcontent']=$content;//最后一条评论内容
            $temp['address']=$address;//最后一条评论地址
            $temp['lasttime']=time();//最后一条评论时间
            //查询该评论属于哪一个评论分块
            $boxid = $pubUid['boxid'];
            if(empty($boxid)){
                $this->throwError('被回复的评论不存在');
            }else{
                //更新该评论分块的内容
                $is_res = $ArticleModel->saveBox($boxid,$temp,$save);
            }              
            
        }
		
		if(!$is_res){
            $msg['flag'] = 0;
            $msg['msg'] = '评论失败';
        }else{//评论成功
            //判断当前用户是否已被加入敏感用户人群，加入了则屏蔽评论
            $s_data = array();
            $s_data['uid'] = $uid;
            $s_data['comment_id'] = (int)$is_res;
            $s_data['type'] = empty($recomid)?1:2;
            $this->checkUidSensitive($s_data, $header);
            //返回参数处理
            $data['comid'] = (int)$is_res;//评论id
            $data['replied_comid']= (int)$recomid;//被回复的评论id
            $data['diggnums'] = 0;//赞次数
            $data['content'] = $content;//评论内容
            $data['addtime'] = date('Y-m-d H:i:s',time());//评论时间
            $data['uid'] = $uid;//评论用户id

            //评论用户信息
            $data['userinfo'] = array(
                'nickname' => empty($userallinfo['nickname'])?"":$userallinfo['nickname'],//评论用户昵称
                'avatar_image' => empty($userallinfo['avatar_image'])?"":$userallinfo['avatar_image'],//评论用户头像
                'ulevel' => $userallinfo['user_level'],//评论用户等级
                'certification_level' => empty($userallinfo['certified_info'])?"":$userallinfo['certified_info'],//评论用户认证等级
            );
           
            $data['select_name'] = isset($pubuserinfo['nickname']) && $pubuserinfo['nickname']?$pubuserinfo['nickname']:"";//被评论的用户名称
            
            //发布评论任务
            // $TaskService = new TaskService();
            // $TaskService->postTask($uid,'doforumcom',$userallinfo);
            
            $msg['flag'] = 1;
            $msg['msg'] = '发布成功';
            $msg['data'] = $data;
        }
		
		
		return $msg;
    }
    public function checkUidSensitive($data,$header)
    {
        $url = 'mall/sensitiveuser/addSensitive';
        $data['source'] = 5;
        $result = $this->PubForward($header, $data, $url, 2);
        if (!empty($result['data']['is_sensitive_user']) && $result['data']['is_sensitive_user']==1) {
            //如果该评论用户是敏感用户，则将评论或回复屏蔽
            Db::name('article_comment')->where(['comid'=>$data['comment_id']])->update(['is_show'=>0]);
        }
    }


}
<?php
declare (strict_types=1);

namespace app\service\command;


use app\model\WineAcademyModel;
use app\service\elasticsearch\Document;
use app\service\elasticsearch\types\WineAcademyDocument;
use think\facade\Db;


class SynAcademyToEsCommand
{
    private $wineAcademyModel;
    private $document;
    private $model;

    private function init()
    {
        $this->wineAcademyModel = new WineAcademyModel();
        $this->document = new Document();
    }

    public function exec($model)
    {
        $this->init();
        $this->model = $model;

        $waitSynData = $this->model->where(['type' => 1])->select()->toArray();
        if(empty($waitSynData)){
            exit;
        }
        $waitSynIdsArr = array_column($waitSynData, 'task_id');
        $reWaitSysData = array_column($waitSynData, null, 'task_id');

        $fields = [
            'id',
            'price',
            'title',
            'city_name',
            'deadline'
        ];

        $data = $this->wineAcademyModel->field($fields)
            ->where('deadline','>=',time())
            ->whereIn('id',$waitSynIdsArr)
            ->order('add_time desc')
            ->select()->toArray();

        foreach ($data as &$item) {
            $item['resource'] = 'academy';
            $item['effect_time_range'] = ['gte' => 0, 'lte' => 4747881840];
            unset($item['deadline']);
            $wineAcademyDocument = new WineAcademyDocument($item['id']);
            $operate = !empty($reWaitSysData[$item['id']]) ? $reWaitSysData[$item['id']]['operate'] : 'create';
            $wineAcademyDocument->setIndex(env('ES.PREFIX').'academy_'.env('ES.ENV'));
            $wineAcademyDocument->setOperate($operate);
            $wineAcademyDocument->setHit($item);
            $wineAcademyDocument->toArray();
            $result = $this->document->setDocument($wineAcademyDocument)->$operate();
            $this->delData($result);
        }
    }

    private function delData($result)
    {
        $this->model->where(['task_id' => $result['_id']])->delete();
    }
}

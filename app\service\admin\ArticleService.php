<?php

namespace app\service\admin;

use AlibabaCloud\SDK\Cdn\V20180510\Cdn;
use AlibabaCloud\SDK\Cdn\V20180510\Models\RefreshObjectCachesRequest;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use app\BaseService;
use app\model\ArticleCommentModel;
use app\model\ArticleModel;
use app\service\es\elasticsearch\ElasticSearchService;
use app\validate\ArticleValidate;
use Darabonba\OpenApi\Models\Config;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\Validate;
use think\Log;

/**
 * 后台管理--酒闻资讯
 */
class ArticleService extends BaseService {
    /**
     * 后台管理--酒闻列表
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function getAdminArticleList($param,$header){            

        //查询数据
        $ArticleModel = new ArticleModel();
        $data = $ArticleModel->getAdminArticleList($param);

        //数据处理
        $arr = [];
        if(!empty($data['list'])){            
            foreach($data['list'] as $key => $val){
                //文章缩略图图片处理
                $val['img'] = $val['img']?env('OSS.ALIURL').$val['img']:'';
                $val['updatetime'] = date('Y-m-d H:i:s',$val['updatetime']);
                // $val['is_hot'] = $val['is_hot']==1?'是':'否';
                // $val['is_index'] = $val['is_index']==1?'是':'否';
                // $val['status'] = $val['is_index']==1?'已审核':'待审核';
                $arr[] = $val;
            }
        }

        $data['list'] = $arr;

        return  $data;
    }


    /**
     * 后台管理--添加酒闻
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function operateArticle($param,$header){        
        //数据验证  
        $ArticleValidate = new ArticleValidate();
        $is_validate = $ArticleValidate->goCheck($param);
        if($is_validate !== true){
            $this->throwError($is_validate);
        }        
        
        //解析token
        $userinfo = $this->analysisToken($header['securitycheckval']);
        $userinfo['id']       = request()->header('vinehoo-uid', 0);
        $p_vh_vos_name        = request()->header('vinehoo-vos-name', null);
        $p_vh_vos_name        = $p_vh_vos_name ? base64_decode($p_vh_vos_name) : '';
        $userinfo['username'] = $p_vh_vos_name;
//        $userinfo['id'] = 1;
//        $userinfo['username'] = '测试人员';

        $data['title'] = $param['title'];//标题
        $data['cate_id'] = isset($param['cate_id'])?$param['cate_id']:0;//所属分类
        $data['topic_id'] = isset($param['topic_id'])?$param['topic_id']:0;//话题id
        $data['topic_name'] = isset($param['topic_name'])?$param['topic_name']:"";//话题内容
        $data['country'] = isset($param['country'])?$param['country']:"";//国家
        $data['img'] = isset($param['img'])?$param['img']:"";//图片
        $data['url'] = $data['img'];
        $data['abst'] = $param['abst'];//摘要简介

        // 处理文章内容字段，支持新旧两种格式
        if (isset($param['info'])) {
            $data['info'] = $param['info'];//详细内容
        }
        if (isset($param['md_info'])) {
            $data['md_info'] = $param['md_info'];//详细内容
        }

        $data['countrys'] = isset($param['countrys'])?$param['countrys']:"";//关联国家ID
        $data['villages'] = isset($param['villages'])?$param['villages']:"";//关联酒庄ID
        $data['areas'] = isset($param['areas'])?$param['areas']:"";//关联产区ID
        $data['wids'] = isset($param['wids'])?$param['wids']:"";//关联酒款ID
        $data['period'] = isset($param['period'])?$param['period']:"";//关联期数
        $data['ordid'] = isset($param['ordid'])?$param['ordid']:0;//排序
        $data['is_hot'] = isset($param['is_hot'])?$param['is_hot']:0;//是否热门 1是 0否
        $data['is_index'] = isset($param['is_index'])?$param['is_index']:0;//是否首页显示 1是 0否
        $data['status'] = isset($param['status'])?$param['status']:0;//上线状态
        $data['source'] = isset($param['source'])?$param['source']:"";//作者/来源
        $data['id'] = isset($param['id'])?$param['id']:0;//作者/来源
        $data['operate_admin_id'] = isset($userinfo['id'])?$userinfo['id']:0;//操作人id

        // 判断文章是否包含图片，检查 info 和 md_info 两个字段
        $info_content = isset($param['info']) ? $param['info'] : '';
        $md_info_content = isset($param['md_info']) ? $param['md_info'] : '';

        if(strpos($info_content,'<img ') !== false || strpos($md_info_content,'![') !== false || strpos($md_info_content,'<img ') !== false ){
            $data['hasimg'] = 1;
        }else{
            $data['hasimg'] = 0;
        }

        $ArticleModel = new ArticleModel();

        if(isset($param['id']) && !empty($param['id'])){
            $article = $ArticleModel->getArticleInfo($param['id']);
            $data['up_time'] = date('Y-m-d H:i:s',$article['updatetime']);//上一次更新时间
        }else{
            $data['add_time']=date('Y-m-d H:i:s',time());//添加时间
            $data['up_time']=date('Y-m-d H:i:s',time());//上一次更新时间
            $data['adname'] = $userinfo['username'];
        }

        $data['updatetime'] = time();//更新时间
        $data['adminid'] = $userinfo['id'];
        $data['update_name'] = $userinfo['username'];
        $is_res = $ArticleModel->operateArticle($data);
        if($is_res){
            $res['flag'] = 1;
            $res['msg'] = '操作成功';
        }else{
            $res['flag'] = -1;
            $res['msg'] = '操作失败';
        }

        return $res;
    }


    /**
     * 更改酒闻状态
     *
     * @param array $param 提交参数
     * @param array $header 头部参
     * @return array $res 返回结果
     */
    public function changeArticleStatus($param,$header){
        if(empty($param['type'])){
            $this->throwError('类型不能为空');
        }

        if($param['type'] == 1){//热搜
            $save['is_hot'] = $param['status'];
        }elseif($param['type'] == 2){//推荐
            $save['is_index'] = $param['status'];
        }elseif($param['type'] == 3){//上线状态 (文章状态)
            $save['status'] = $param['status'];
        }

        $ArticleModel = new ArticleModel();

        $article = $ArticleModel->getArticleInfo($param['id']);
        
        $save['up_time'] = date('Y-m-d H:i:s',$article['updatetime']);//上一次更新时间
        $save['updatetime'] = time();//更新时间
        $save['id'] = $param['id'];//文章id

        $is_res = $ArticleModel->changeArticleStatus($save);
        if($is_res){
            if (isset($save['status']) && $save['status']==1){
                //启用时删除敏感用户数据
                $s_data = array();
                $s_data['uid'] = $article['uid']??'';
                $s_data['comment_id'] = (int)$article['id'];
                $s_data['type'] = 3;//酒闻帖子
                //$this->delSensitive($s_data, $header);接口暂时没有
            }
            $res['flag'] = 1;
            $res['msg'] = '操作成功';
        }else{
            $res['flag'] = 0;
            $res['msg'] = '操作失败';
        }

        return $res;
    }
    /**
     * 删除敏感用户数据
     * @param $data
     * @param $header
     */
    public function delSensitive($data,$header)
    {
        $url = 'mall/sensitiveuser/delSpecifyData';
        $data['source'] = 5;//酒闻帖子
        $this->PubForward($header, $data, $url, 2);
    }

    /**
     * 酒闻详情
     *
     * @param array $param 查询条件
     * @return array $article 返回数据
     */
    public function getArticleInfo($param){
        $article = (new ArticleModel())->getArticleInfo($param['id']);
        return $article;
    }


    /**
     * 删除酒闻
     *
     * @param array $param 提交参数
     * @param array $header 头部参
     * @return array $res 返回结果
     */
    public function delArticle($param,$header){        
        if(!isset($param['id']) || empty($param['id'])){
            $this->throwError('酒闻id不能为空');
        }

        $ArticleModel = new ArticleModel();

        $id = explode(',',$param['id']);
        $is_res = $ArticleModel->delArticle($id);

        if($is_res){
            $res['flag'] = 1;
            $res['msg'] = '操作成功';
        }else{
            $res['flag'] = 0;
            $res['msg'] = '操作失败';
        }

        return $res;
    }


    /**
     * 酒闻排序
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function articleSort($param,$header){
        //数据验证
        $validate = Validate::rule('id|酒闻id', 'require|number')
            ->rule([
                'ordid|排序值'  => 'require|number',
            ]);

        if (!$validate->check($param)) {
            $this->throwError($validate->getError());
        }

        $ArticleModel = new ArticleModel();

        $save['id'] = $param['id'];
        $save['ordid'] = $param['ordid'];
        $is_res = $ArticleModel->changeArticleStatus($save);

        if($is_res === false){
            $msg['flag'] = 0;
            $msg['msg'] = '操作失败';
        }else{
            $msg['flag'] = 1;
            $msg['msg'] = '操作成功';

        }
        return $msg;
    }

    /**
     * 酒闻列表查询
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function getArticleList($param,$header){
        $securitycheckval = $header['securitycheckval'];//token值

        //验证token，token过期重新授权登录(未登陆可以不验证token)
        if($securitycheckval || $securitycheckval != ''){
            $checkInfo = $this->checkRedisToken($securitycheckval);//验证
            $userinfologin = $checkInfo['data'];
            $param['uid']=$userinfologin['uid'];
        }

        if($param['limit'] > 30){
            $this->throwError('分页数量最大不能超过30条');
        }

        //查询数据
        $ArticleModel = new ArticleModel();
        $data = $ArticleModel->getArticleList($param);

        //数据处理
        $arr = [];
        if(!empty($data['list'])){
            foreach($data['list'] as $key => $val){
                //文章缩略图图片处理
                $val['img'] = env('OSS.ALIURL').$val['img'];
                $arr[] = $val;
            }
        }

        //帖子批量添加浏览量
        //控制开启批量添加流量量的config
//        $article_views_add = Db::connect('mall')->name('config')->where('name','article_views_add')->value('value');
//        if(!empty($article_views_add)){
//            $articleId = array_column($data['list'],'id');
//            if(!empty($articleId)) $ArticleModel->batchViewAdd($articleId);
//        }

        $data['list'] = $arr;

        $msg['flag'] = 1;
        $msg['msg'] = '查询成功';
        $msg['data'] = $data;

        return $msg;
    }


    /**
     * 酒闻详情查询
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function getArticleDetails($param,$header){
        //$securitycheckval = $header['securitycheckval'];//token值

        //验证token，token过期重新授权登录(未登陆可以不验证token)
//        if($securitycheckval || $securitycheckval != ''){
//            $checkInfo = $this->checkRedisToken($securitycheckval);//验证
//            $userinfo = $checkInfo['data'];
//            //查询用户信息
//            //$myuserinfo = $this->getUidAllInfo($header, $userinfo['uid'], $param['uuid']);
//        }

//        if(empty($param['id'])){
//            $this->throwError('资讯id不能为空');
//        }

        //查询数据
        $ArticleModel = new ArticleModel();
        $data = $ArticleModel->getArticleDetails($param['id']);

        //更新浏览量
        $ArticleModel->updateArticleInfo($param['id'],'viewnums',1);
        //查询文章是否收藏
        if(isset($header['vinehoo-uid'])){
            $is_collect = $ArticleModel->getCollectInfo($param['id'],$header['vinehoo-uid']);
        }else{
            $is_collect = 0;
        }

        if(empty($data)){
            $data = [];
        }

        $data['img'] = empty($data['img'])?'':env('OSS.ALIURL').$data['img'];

        if (!empty($data['md_info'])) {
            $data['info_field'] = 'md_info';
        } else {
            $data['info_field'] = 'info';
        }

        $data['is_collect'] = $is_collect?1:0;//是否收藏 1已收藏 0收藏

        if(!empty($data['topic_id'])){
             $topic_info = getTopicInfo($data['topic_id']);
             $data['topic_info'] = $topic_info['data'];
        }else{
            $data['topic_info'] = [];
        }

        //分享地址
        $data['share_link'] = Config('config')['WEIBO_SHARE_URL']."/web-static/details/wineNewsDetail.html?id={$param['id']}";

        //酒闻详情地址
        $data['detail_link'] = Config('config')['WEIBO_SHARE_URL']."/h5/wineNewsDetail/{$param['id']}";

        $msg['flag'] = 1;
        $msg['msg'] = '查询成功';
        $msg['data'] = $data;

        return $msg;
    }

    /**
     * 查询用户是否收藏
     * @param $param
     * @return array|int
     */
    public function getIsCollect($param)
    {
        try {
            $ArticleModel = new ArticleModel();
            //更新浏览量
            $ArticleModel->updateArticleInfo($param['id'],'viewnums',1);
            //查询文章是否收藏
            if(isset($param['uid']) && !empty($param['uid'])){
                $is_collect = $ArticleModel->getCollectInfo($param['id'],$param['uid']);
               if (!$is_collect){
                   $is_collect=0;
               }
            }else{
                $is_collect = 0;
            }
            return ['is_collect'=> $is_collect];
        } catch (\Exception $e) {
            $this->throwError('查询失败');
        }
    }
    /**
     * 所有酒闻生成js
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function createArticleJs()
    {
        $where []= ['status','=',1];
        $where []= ['cstatus','<>',-1];
//        $res = Db::name('article')
//            ->field("id")->select()->toArray();
//        if (empty($res)) {
//            return [];
//        }
        $result=[];
        Db::name("article")->where($where)->chunk(100,function ($res){
            foreach ($res as $v){
                $res= $this->createJson($v['id']);
                if ($res  && isset($res['error_code']) && $res['error_code'] == 0){
                    $result[]=$v['id'];
                }
            }
        });

        return $result;
    }
    /**
     * @param $id
     * @return bool
     */
    public function createJson($id )
    {
        $ArticleModel = new ArticleModel();
        $data = $ArticleModel->getArticleDetails($id);
        //
        if(empty($data)){
            \think\facade\Log::error($id."json文件生成失败");
        }

        if (!empty($data['md_info'])) {
            $data['info_field'] = 'md_info';
        } else {
            $data['info_field'] = 'info';
        }

        if(!empty($data['topic_id'])){
            $topic_info = getTopicInfo($data['topic_id']);
            $data['topic_info'] = $topic_info['data'];
        }else{
            $data['topic_info'] = [];
        }
        $data['img'] = empty($data['img'])?'':env('OSS.ALIURL').$data['img'];
        //分享地址
        $data['share_link'] = Config('config')['WEIBO_SHARE_URL']."/web-static/details/wineNewsDetail.html?id={$id}";
        //酒闻详情地址
        $data['detail_link'] = Config('config')['WEIBO_SHARE_URL']."/h5/wineNewsDetail/{$id}";
        #商品详情
        $data['period_info'] = [];
        if (!empty($data['period'])) {
            $period = explode(",",$data['period']);
            $searchService = new ElasticSearchService();
            $index = 'periods';
            $input = [
                'index' => [$index],
                'terms' => [['id' => $period]],
                'source' => ['id','title', 'brief','banner_img','product_img','price'],
            ];
            $period = $searchService->getDocumentList($input);
            foreach ($period['data'] as &$v){
                $v['banner_img'] = imagePrefix($v['banner_img']);
                $v['product_img'] = imagePrefix($v['product_img']);
            }
            $data['period_info'] = $period['data'];
        }
        $re['error_code'] = 0;
        $re['data'] = $data;
        $re['error_msg'] = '';
        $json_data = json_encode($re);
        // 上传到 oss
        $oss_re = $this->uploadFile($id, $json_data);
        return  $oss_re;

    }

    /**
     * 上传oss
     * @param int $id
     * @param string $json_data
     * @return array
     */
    public function uploadFile(int $id, string $json_data): array
    {
        // 阿里云 oss 账号
        $accessKeyId = env('OSS.ACCESSKEYID');
        $accessKeySecret = env('OSS.ACCESSKEYSECRET');
        // oss 数据中心域名
        $endpoint = env('OSS.ENDPOINT');
        // oss 存储空间名称
        $bucket = env('OSS.BUCKET');
        // 上传到 oss 目录地址
        $object = 'vinehoo/client/news/article/' . $id . '.json';
        $ossClient = new \OSS\OssClient($accessKeyId, $accessKeySecret, $endpoint);
        $result = $ossClient->putObject($bucket, $object, $json_data);
        if (!$result) {
            return [];
        }
        // 刷新 CDN 文件
         self::CDNrefreshObject($id);
        return $result;
    }

    /**
     * 使用AK&SK初始化账号Client
     * @param string $accessKeyId
     * @param string $accessKeySecret
     * @return Cdn Client
     */
    public static function createClient()
    {
        $config = new Config([
            // AccessKey ID
            "accessKeyId" => 'LTAI5tA399hBWprfBqq9m2hj',
            // AccessKey Secret
            "accessKeySecret" => '******************************'
        ]);
        // 访问的域名
        $config->endpoint = 'cdn.aliyuncs.com';

        return new Cdn($config);
    }
    /**
     * @param int $period
     * @return void
     */
    public static function CDNrefreshObject(int $period)
    {
        $client = self::createClient();
        $refreshObjectCachesRequest = new RefreshObjectCachesRequest([
//            'objectPath' => 'images.vinehoo.com'.'vinehoo/client/commodities/periods/' . $period . '.json'
            'objectPath' => env('OSS.ALIURL') . '/vinehoo/client/commodities/periods/' . $period . '.json' .
                '?t=1&isJson=true&id=' . $period
        ]);
        $runtime = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $result = $client->refreshObjectCachesWithOptions($refreshObjectCachesRequest, $runtime);
        } catch (\Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            // 如有需要，请打印 error
            Utils::assertAsString($error->message);
        }
    }

    /**
     * 酒闻收藏
     *
     * @param array $param 提交参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function getArticleCollect($param,$header){
        //$securitycheckval = $header['securitycheckval'];//token值

        //验证token，token过期重新授权登录(未登陆可以不验证token)
        //$checkInfo = $this->checkRedisToken($securitycheckval);//验证
        $userinfo['uid'] = $header['vinehoo-uid'];
        //查询用户信息
        //$myuserinfo = $this->getUidAllInfo($header, $userinfo['uid'], $param['uuid']);
//        $myuserinfo = $userinfo['loginname'];//电话号码
//        if(empty($myuserinfo)){
//            $this->throwError('请绑定手机');
//        }

        if(empty($param['id'])){
            //$this->throwError('资讯id不能为空');
            $msg['flag'] = 0;
            $msg['msg'] = '资讯id不能为空';
            return  $msg;
        }

        //查询数据
        $ArticleModel = new ArticleModel();
        $data = $ArticleModel->getArticleDetails($param['id']);
        if(empty($data)){
            //$this->throwError('该资讯不存在');
            $msg['flag'] = 0;
            $msg['msg'] = '该资讯不存在';
            return  $msg;
        }

        if($param['status'] == 1){//类型 0取消收藏 1收藏
            //查询文章是否收藏
            $is_collect = $ArticleModel->getCollectInfo($param['id'],$userinfo['uid']);
            if(!empty($is_collect)){
//                $this->throwError('该资讯已收藏');
                $msg['flag'] = 0;
                $msg['msg'] = '该资讯已收藏';
                return  $msg;
            }

            //添加收藏
            $is_res  = $ArticleModel->AddCollectArticle($param['id'],$userinfo['uid']);

            if($is_res){
                $msg['flag'] = 1;
                $msg['msg'] = '收藏成功';
            }else{
                $msg['flag'] = 0;
                $msg['msg'] = '收藏失败';
            }
        }else{
            //取消收藏
            $is_res  = $ArticleModel->delCollectArticle($param['id'],$userinfo['uid']);
            if($is_res === false){
                $msg['flag'] = 0;
                $msg['msg'] = '取消收藏失败';
            }else{
                $msg['flag'] = 1;
                $msg['msg'] = '取消收藏成功';
            }
        }

        return $msg;
    }



    /**
     * 资讯评论列表
     *
     * @param array $param 查询使用参数
     * @param array $header 头部参数
     * @return array $msg 查询结果
     */
    public function getArticleComment($param,$header){
        //$securitycheckval = $header['securitycheckval'];//token值

        $userinfo['uid'] = $header['vinehoo-uid']??"";
        $param['uid'] = $header['vinehoo-uid']??"";
        $param['uuid'] = !empty($param['uuid'])?$param['uuid']:'';
        if($param['limit'] > 30){
            $this->throwError('分页数量最大不能超过30条');
        }

        //数据处理
        $ArticleModel = new ArticleModel();
        $articlecommentInfo = $ArticleModel->getArticleComment($param);//查询资讯评论
        $commentInfo = $articlecommentInfo['list'];

        $data = [];
        if(!empty($commentInfo)){
            $commentidarr = array_column($commentInfo,'comid');//获取资讯评论id
            $oneuidarr = array_column($commentInfo,'uid');//获取资讯评论用户id
            $boxidarr = array_column($commentInfo,'boxid');//获取资讯评论分块id


            //获取点赞信息
//            if(!empty($myuserinfo)){
            if($param['uid']){ //判断与用户是否存在
                $commentidstr = implode(',',$commentidarr);
                $wherestr = "articleid={$param['id']} and comid in ($commentidstr) and uid={$userinfo['uid']}";//查询条件
                $diggInfo = $ArticleModel->getBatchDigg($wherestr);
                $diggarr = array_column((array)$diggInfo, null, 'comid');
            }

            //查询帖子评论的用户信息
            $oneuserinfoarr =  $this->getUserInfoArr($header, $oneuidarr, $param['uuid']);
            $oneuserinfoarrnew = array_column($oneuserinfoarr, null, 'uid');

            //查询帖子评论的回复评论
            $replycommenInfo = $ArticleModel->getArticleReplyComment($boxidarr,$param['uid']);
            //获取回复评论点赞信息
            if($param['uid'] && !empty($replycommenInfo)){ //判断与用户是否存在
                $replycommentdarr = array_column($replycommenInfo,'comid');//获取资讯评论id
                $replycommentidstr = implode(',',$replycommentdarr);
                $replywherestr = "articleid={$param['id']} and comid in ($replycommentidstr) and uid={$userinfo['uid']}";//查询条件
                $replydiggInfo = $ArticleModel->getBatchDigg($replywherestr);
                $replydiggarr = array_column((array)$replydiggInfo, null, 'comid');
            }

            //获取回复评论用户id
            $twouidarr = array_column($replycommenInfo,'uid');
            //查询回复评论的用户信息
            $twouserinfoarr =  $this->getUserInfoArr($header, $twouidarr, $param['uuid']);
            $twouserinfoarrnew = array_column($twouserinfoarr, null, 'uid');

            //获取回复评论用户id
            $threeuidarr = array_column($replycommenInfo,'reuid');
            //查询被回复评论的用户信息
            $threeuserinfoarr =  $this->getUserInfoArr($header, $threeuidarr, $param['uuid']);
            $threeuserinfoarrnew = array_column($threeuserinfoarr, null, 'uid');

            foreach($commentInfo as $ckey => $cval){//帖子评论数据
                $cval['is_digg'] = !isset($diggarr[$cval['comid']]['comid'])?0:1;//是否可以点赞 1否 0是
                $cval['addtime_keyword'] = mdate($cval['addtime']);

                //评论用户信息
                $userinfo = [
                    'nickname'=>$oneuserinfoarrnew[$cval['uid']]['nickname']??"",
                    'avatar_image'=>@$oneuserinfoarrnew[$cval['uid']]['avatar_image']?env("ALIURL").$oneuserinfoarrnew[$cval['uid']]['avatar_image']:"",
                    'ulevel'=>$oneuserinfoarrnew[$cval['uid']]['user_level']??"",
                    'certification_level'=>$oneuserinfoarrnew[$cval['uid']]['certified_info']??"",
                    'type'=>$oneuserinfoarrnew[$cval['uid']]['type']??"",
                ];
                $cval = array_merge($cval,$userinfo);

                $cval['replyCommentInfo'] = [];
                //回复评论数据处理
                foreach($replycommenInfo as $rkey => $rval){
                    $rval['is_digg'] = !isset($replydiggarr[$rval['comid']]['comid'])?0:1;//是否可以点赞 1否 0是
                    $rval['addtime_keyword'] = mdate($rval['addtime']);

                    $rval['article_username'] =  isset($threeuserinfoarrnew[$rval['reuid']])?$threeuserinfoarrnew[$rval['reuid']]['nickname']:'';//被评论的评论用户昵称

                    //评论回复用户信息处理
                    $reuserinfo = [
                        'nickname'=>$twouserinfoarrnew[$rval['uid']]['nickname']??"",
                        'avatar_image'=>@$twouserinfoarrnew[$rval['uid']]['avatar_image']?env("ALIURL").$twouserinfoarrnew[$rval['uid']]['avatar_image']:"",
                        'ulevel'=>$twouserinfoarrnew[$rval['uid']]['user_level']??"",
                        'certification_level'=>$twouserinfoarrnew[$rval['uid']]['certified_info']??"",
                        'type'=>$twouserinfoarrnew[$rval['uid']]['type']??"",
                    ];
                    $rval = array_merge($rval,$reuserinfo);

                    if($cval['boxid'] == $rval['boxid']){
                        $cval['replyCommentInfo'][] = $rval;
                    }

                }

                $data[] = $cval;
            }

        }
        $articlecommentInfo['list'] = $data;

        $msg['flag'] = 1;
        $msg['msg'] = '查询成功';
        $msg['data'] = $articlecommentInfo;

        return $msg;
    }


    /**
     *资讯评论点赞
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function doDigg($param,$header)
    {

        $uid = $header['vinehoo-uid'];


        $ArticleModel = new ArticleModel();
        if($param['type'] == 1){//资讯点赞日志
            //查询资讯
            $data = $ArticleModel->getArticleDetails($param['id']);
            if(empty($data)){
                $msg['flag'] = 0;
                $msg['msg'] = '该资讯不存在';
                return $msg;
            }

            $is_digg = $ArticleModel->getArticleLog($param['id'],$uid);
            if(!empty($is_digg)){
                $msg['flag'] = 0;
                $msg['msg'] = '已点过赞';
                return $msg;

            }

            //资讯点赞日志数据
            $log = array(
                'aid' => $param['id'],//文章id
                'uid' => $uid,//用户id
                'versions' => $header['vinehoo-client-version'],//客户端版本号
                'ip' => $_SERVER['SERVER_ADDR'],//ip地址
                'addtime' => date('Y-m-d H:i:s',time())//点赞时间
            );
            $is_res = $ArticleModel->InsertArticleLog($log);
        }else if($param['type'] == 2){//评论点赞日志
            //查询资讯
            $data = $ArticleModel->getComment($param['id']);
            if(empty($data)){
                $msg['flag'] = 0;
                $msg['msg'] = '该评论不存在';
                return $msg;
            }

            $newsid = $data['aid'];
            $oneuserinfoarr =  $this->getUserInfoArr($header, [$data['uid']]);
            if(isset($oneuserinfoarr['list'])) throw new ValidateException("用户不存在,无法点击评论");
            //推送
            if((is_array($oneuserinfoarr) && count($oneuserinfoarr)>0 &&$oneuserinfoarr[0]['is_pushconbtn'] ==0) || $data['uid'] == $uid ){
                $is_push = 0;//不推送
            }else{
                $is_push = 1;//推送
            }

            $is_digg = $ArticleModel->getCommentLog($param['id'],$uid);
            if(!empty($is_digg)){
                $msg['flag'] = 0;
                $msg['msg'] = '已点过赞';
                return $msg;
            }

            //评论点赞日志数据
            $log = array(
                'articleid' => $data['aid'],//文章id
                'comid' => $param['id'],//评论id
                'uid' => $uid,//用户id
                'addtime' => date('Y-m-d H:i:s',time())//点赞时间
            );
            $is_res = $ArticleModel->InsertCommentLog($log);
        }

        if($is_res){

            $user =  $this->getUserInfoArr($header, [$uid]);
            if($param['type'] == 2){

                $push_data = [
                    'is_push' => $is_push,
                    'uid' => $data['uid'],//被推送人id
                    'title' => '评论点赞',
                    'content' => $user[0]['nickname'] . '给您的评论“'.$data['content'].'”点赞了，快去看看吧~',
                    'data_type' => 14,
                    'created_id' => 0,
                    'label' => 'wineSmellsDetail', //跳转酒闻详情
                    'custom_param' => ['id' => $newsid],//酒闻ID
                    'data' => [
                        'content' => $data['content'],
                        'nickname' => $user[0]['nickname'],
                        'type_title' => '您的评论',
                        'type' => 2  //0:帖子 1:短视频 2:评论 3:酒闻 4:商品
                    ]
                ];
                if($data['uid'] != $uid) $this->pushMsg($push_data);
//                echo json_encode($push_data);
            }

            $msg['flag'] = 1;
            $msg['msg'] = '点赞成功';
        }else{
            $msg['flag'] = 0;
            $msg['msg'] = '已点过赞';
        }

        return $msg;

    }

    /**
     * Description:酒闻资讯--资讯、评论取消点赞
     * Author: zrc
     * Date: 2023/7/6
     * Time: 13:28
     * @param $params
     * @return bool
     * @throws \think\db\exception\DbException
     */
    public function cancelLike($params)
    {
        switch ($params['type']){
            case 1:
                $article_log_id = Db::name('article_log')->where(['aid'=>$params['id'],'uid'=>$params['uid']])->value('id');
                if($article_log_id){
                    Db::name('article_log')->where(['id'=>$article_log_id])->delete();
                    Db::name('article')->where(['id'=>$params['id']])->dec('diggnums')->update();
                }
                break;
            case 2:
                $article_digg_log_id = Db::name('article_digg_log')->where(['comid'=>$params['id'],'uid'=>$params['uid']])->value('id');
                if($article_digg_log_id){
                    Db::name('article_digg_log')->where(['id'=>$article_digg_log_id])->delete();
                    Db::name('article_comment')->where(['comid'=>$params['id']])->dec('diggnums')->update();
                }
                break;
        }
        return true;
    }

    public function pushMsg($param)
    {
        $domain = env('ITEM.APPPUSH_URL');
        $url = $domain.'/apppush/v3/push/single';

        $json_param = json_encode($param);
        $result = http_post_json($url,$json_param);
        $result_arr = json_decode($result);
        if(!$result_arr){
            Log::ERROR("消息推送失败");
        }
    }



    /**
     * 酒闻收藏列表查询
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function getMyArticleCollectList($param,$header){
        //$securitycheckval = $header['securitycheckval'];//token值

        //验证token，token过期重新授权登录(未登陆可以不验证token)
        //$checkInfo = $this->checkRedisToken($securitycheckval);//验证
        //$userinfo = $checkInfo['data'];
        $userinfo['uid'] = $header['vinehoo-uid'];
        //查询用户信息
        //$myuserinfo = $this->getUidAllInfo($header, $userinfo['uid'], $param['uuid']);
        //$myuserinfo = $userinfo['loginname'];//电话号码
//        if(empty($myuserinfo)){
//            $this->throwError('请绑定手机');
//        }

        if($param['limit'] > 30){
            $this->throwError('分页数量最大不能超过30条');
        }

        //查询数据
        $ArticleModel = new ArticleModel();
        $data = $ArticleModel->getMyArticleCollectList($param,$userinfo['uid']);

        //数据处理
        $arr = [];
        if(!empty($data['list'])){
            foreach($data['list'] as $key => $val){
                //文章缩略图图片处理
                $val['img'] = env('OSS.ALIURL').$val['img'];
                //.env('OSS.ALIURL')
                $arr[] = $val;
            }
        }

        $data['list'] = $arr;

        $msg['flag'] = 1;
        $msg['msg'] = '查询成功';
        $msg['data'] = $data;

        return $msg;
    }


    /**
     * 微博发表评论
     *
     * @param array $param 查询条件参数
     * @param string $header header参数
     * @return array $msg 返回信息
     */
    public function MakeCommentOn($param,$header,$admin = 0){


        $articleInfo = Db::name('article')->field('id,status,title')->where('id','=',$param['id'])->find();
        if(empty($articleInfo) || $articleInfo['status'] == 0){
            $msg['flag'] = -1;
            $msg['msg'] = '酒闻不存在,或者酒闻已经下线';
            return $msg;
        }

        //验证是否包含敏感词
        if(empty($admin)){ //用户发起的评论

        }else{
            //是否把评论人改为马甲
            $header['vinehoo-uid'] = empty($param['vuid'])?$header['vinehoo-uid']:$param['vuid'];
        }


        $uid = $header['vinehoo-uid'];//用户id
        $aid = $param['id'];//帖子id
        //敏感词判断
        //$word_state = $this->filterSensitiveWords($param['content']);
        $content = $param['content'];//评论内容
        $address = isset($param['address'])?$param['address']:'';//地址
        $reuid = isset($param['reuid'])?$param['reuid']:'';//被回复评论的用户id
        $recontent = isset($param['recontent'])?$param['recontent']:'';//被回复的评论内容
        $recomid = isset($param['recomid'])?$param['recomid']:'';//被回复的评论id(未传入表示第一条评论)
        $param['uuid'] = isset($param['uuid'])?$param['uuid']:'';//设备id
        $emoji_image = isset($param['emoji_image'])?$param['emoji_image']:'';//设备id

        /*//验证用户是否被禁言
        $WeiboService = new WeiboService();
        $userallinfo = $WeiboService->verfiyillegal($header,$uid,$param['uuid']);*/


        $save=array();//评论入库信息
        //评论图片信息处理
        $save['uid']=$uid;//评论用户id
        $save['recomid']= $recomid?$recomid:0 ;//被回复的评论id
        $save['reuid']=$reuid;//被回复评论的用户id
        $save['aid']=$aid;//文章id
        $save['content']=$content;//评论内容
        $save['address']=empty($address)?"":$address;//评论地址
        $save['recontent']=empty($recontent)?"":$recontent;//回复内容
        $save['addtime']=time();//评论时间
        $save['emoji_image']=$emoji_image;//图片表情包
        #敏感词
        if(isset($param['vuid'])){//马甲
            $save['is_show'] = 0;
//            $save['audit_status'] = 2;//已通过审核
        }else{
            $save['is_show'] = 0;
        }

        //if($word_state == true)
        $pubuserinfo = [];


        Db::startTrans();
        try {

            $ArticleModel = new ArticleModel();

            //判断是评论文章还是回复评论
            if(empty($recomid)){//被回复的评论id如果为空则是评论帖子
                if(strlen($content)>65000) $this->throwError('评论的内容太长！');
                //以评论文章的用户为单位模块
                $temp = array();
                $temp['weibo_id']=$aid;//文章id
                $temp['uid']=$uid;//第一个评论用户id
                $temp['lastuid']=$uid;//最后一个评论用户id
                $temp['lastcontent']=$content;//最后一条评论内容
                $temp['address']=$address;//最后一条评论地址
                $temp['lasttime']=time();//最后一条评论时间
                $temp['type']=1;//评论类型 0微博1资讯


                //添加到评论分块表
                $is_res = $ArticleModel->addBox($temp,$save);
            }else{//不为空，则是回复评论
                if(strlen($content)>65000) $this->throwError('回复的内容太长！');

                //查询评论用户
                $pubUid = $ArticleModel->getIdComment($recomid);

                $temp = array();
                $temp['lastuid']=$uid;//最后一个评论用户id
                $temp['lastcontent']=$content;//最后一条评论内容
                $temp['address']=$address;//最后一条评论地址
                $temp['lasttime']=time();//最后一条评论时间
                //查询该评论属于哪一个评论分块
                $boxid = $pubUid['boxid'];

                if(empty($boxid)){
                    //$this->throwError('被回复的评论不存在');
                    $msg['flag'] = 0;
                    $msg['msg'] = '被回复的评论不存在';
                    return $msg;
                }else{
                    //更新该评论分块的内容
                    $is_res = $ArticleModel->saveBox($boxid,$temp,$save);
                }

            }

            if(!$is_res){
                $msg['flag'] = 0;
                $msg['msg'] = '评论失败';
            }else{//评论成功
                //判断当前用户是否已被加入敏感用户人群，加入了则屏蔽评论
                $s_data = array();
                $s_data['uid'] = $uid;
                $s_data['comment_id'] = (int)$is_res;
                $s_data['type'] = empty($recomid)?1:2;
                //$this->checkUidSensitive($s_data, $header);
                //返回参数处理
                $data['comid'] = (int)$is_res;//评论id
                $data['replied_comid']= (int)$recomid;//被回复的评论id
                $data['diggnums'] = 0;//赞次数
                $data['content'] = $content;//评论内容
                $data['addtime'] = date('Y-m-d H:i:s',time());//评论时间
                $data['uid'] = $uid;//评论用户id

                //评论用户信息
                $data['userinfo'] = array(
                    'nickname' => empty($userallinfo['nickname'])?"":$userallinfo['nickname'],//评论用户昵称
                    'avatar_image' => empty($userallinfo['avatar_image'])?"":$userallinfo['avatar_image'],//评论用户头像
                    'ulevel' => empty($userallinfo['user_level'])?"":$userallinfo['user_level'],//评论用户等级
                    'certification_level' => empty($userallinfo['certified_info'])?"":$userallinfo['certified_info'],//评论用户认证等级
                );

                $data['select_name'] = isset($pubuserinfo['nickname']) && $pubuserinfo['nickname']?$pubuserinfo['nickname']:"";//被评论的用户名称

                //内容审核
                if(!empty($param['vuid'])){
                    $examineData['is_backstage'] = 1;
                    $uid = $param['vuid'];
                }

            }
            Db::commit();
        }catch (\Exception $exception){
//            var_dump($exception->getMessage());
//            Log::ERROR($exception->getMessage());
            $msg['flag'] = -1;
            $msg['msg'] = '评论失败';
            Db::rollback();
            return $msg;
        }

        //用户

            $examineData['uid'] = $uid;
            $examineData['old_comment_id'] = empty($param['old_comment_id'])?'':$param['old_comment_id'];
            $examineData['source'] = 5;
            $examineData['data_id'] = $aid;//酒闻文章id
            $examineData['data_content'] = $articleInfo['title'];//文章标题
            $examineData['comment_id'] = $is_res;//评论id
            $examineData['comment_content'] = $content;//评论内容
            $examineData['expression'] = $emoji_image;//评论图片
            if(!empty($param['recomid'])){

                $reply_expression = ArticleCommentModel::where("comid",$recomid)->value("emoji_image");

                $examineData['type'] = 2;//1评论2回复3内容发布审核
                $examineData['reply_uid'] = $reuid;//被回复用户ID
                $examineData['reply_id'] = $recomid;//被回复id
                $examineData['reply_content'] = $recontent;//被回复内容
                $examineData['reply_expression'] = $reply_expression;//被回复表情
            }else{
                $examineData['type'] = 1;
            }

            $result = examine($examineData);//审核内容

            if($result['error_code'] ==0 ){
                $msg['flag'] = 1;
                $msg['msg'] = '发布成功';
                return $msg;
            }else{//操作失败

                $delrows = Db::name('article_comment')->where("comid",$is_res)->delete();
                if($delrows<1){
                    log::ERROR("内容审核失败、删除失败、评论ID:".$is_res);
                }
                $msg['flag'] = -1;
                $msg['msg'] = '内容审核失败：'.@$result['error_msg'];
                return $msg;
            }

        $msg['flag'] = 1;
        $msg['msg'] = '发布成功';
        $msg['data'] = $data;
        return $msg;
    }

    public function checkUidSensitive($data,$header)
    {
        $url = 'mall/sensitiveuser/addSensitive';
        $data['source'] = 5;
        $result = $this->PubForward($header, $data, $url, 2);
        if (!empty($result['data']['is_sensitive_user']) && $result['data']['is_sensitive_user']==1) {
            //如果该评论用户是敏感用户，则将评论或回复屏蔽
            Db::name('article_comment')->where(['comid'=>$data['comment_id']])->update(['is_show'=>0]);
        }
    }

    /**
     * @Describe:更改评论审核状态
     * <AUTHOR>
     * @Date 2022/4/26 14:05
     */
    public function changeCommentStatus($param){
        //审核状态 1待审核 2已审核 3审核驳回
        if($param['status'] == 2){
            $datasouce = ['audit_status'=>$param['status'],"is_show"=>1];
        }else{
            $datasouce = ['audit_status'=>$param['status'],"is_show"=>0];
        }
        //当前评论
        $articleCommentResult = Db::name('article_comment')->where('comid','=',$param['id'])->find();

        $result = Db::name('article_comment')->where('comid','=',$param['id'])->update($datasouce);


        if($result){
            if($param['status'] == 2){//审核通过
                $articleCommentCount = Db::name('article_comment')->where(['recomid'=>$param['id'],'is_show'=>1])->count();//下面的评论

//                $articleReCommentResult = Db::name('article_comment')->where('comid','=',$param['recomid'])->where('audit_status','=',3)->find();//返回被评论的评论

//                if(!isset($articleReCommentResult)){//被回复评论数据 为
                    Db::name('article')->where('id',$articleCommentResult['aid'])->inc('commentnums',($articleCommentCount+1))->update();
//                }
            }else{
                if($articleCommentResult['recomid'] != 0 && $articleCommentResult['is_show']==1){//二级评论
                    Db::name('article')->where('id',$articleCommentResult['aid'])->dec('commentnums')->update();
                }else if($articleCommentResult['is_show']==1){//一级
                    $articleCommentCount = Db::name('article_comment')->where(['recomid'=>$param['id'],'is_show'=>1])->count();//下面的评论
                    Db::name('article')->where('id',$articleCommentResult['aid'])->dec('commentnums',($articleCommentCount+1))->update();
                }

            }
            $msg['flag'] = 1;
            $msg['msg'] = '操作成功';
            $msg['data'] = "";
        }else{
            $msg['flag'] = 0;
            $msg['msg'] = '操作失败';
            $msg['data'] = "";
        }
        return $msg;
    }

    /**
     * 获取用户收藏数
     * @param $param
     * @return \think\Response
     */
    public function getMyArticleCollectCounts($param)
    {
        $count = Db::name('article_collect')->where(['uid'=>$param['uid']])->count();
        return ["counts"=>$count];
    }
    
}
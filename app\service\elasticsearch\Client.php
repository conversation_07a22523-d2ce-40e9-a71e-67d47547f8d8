<?php
namespace app\service\elasticsearch;

use Elasticsearch\ClientBuilder;

/**
 * es 客户端
 * Class Client
 * @package App\Services\Elasticsearch
 */


class Client
{
    private $index;
    private $data;

    public static function getInstance()
    {
        $hosts = [
            [
                'host' => env('ES.HOST','127.0.0.1'),
                'port' => env('ES.PORT',9200),
                'user' => env('ES.USER','root'),
                'pass' => env('ES.PASS','vinehoo666')
            ]
        ];
        $client = ClientBuilder::create()->setHosts($hosts)->build();
        return $client;
    }

    public static function ping()
    {
        $client = self::getInstance();
        return $client->ping();
    }
    protected function setIndex($index)
    {
        $this->index = $index;

        return $this;
    }

    protected function setData($data)
    {
        $this->data = $data;

        return $this;
    }
}
<?php
declare (strict_types=1);

namespace app\listener;


use app\service\MicroService;
use think\facade\Cache;
use think\facade\Log;
use think\facade\Db;

class DingTalkTask
{
    /**
     * 事件监听处理
     *
     * @return mixed
     */
    public function handle($event)
    {
        $dealType = $event['type'];
        $param = $event['param'];
        if($dealType == 'dingTalk'){ //钉钉推送
            $this->dingTalk($param);
        }

    }

    private function dingTalk($param)
    {
        if( !empty(env('DINGTALK.DONT_COMMENT_PUSH_DINGDING')) ){
            return false;
        }
        $UidArr = array_column($param,'uid');
        $microservice = new MicroService();
        $userData = $microservice->getBulkUserInfoByUserService(['uid' => $UidArr], ['api-version' => 'v1']);

        foreach ($param as $key=>$value){
            $nickname = $userData[$value['uid']]['nickname'];

            // 社区审贴
                        $domain = env("DINGTALK.SHEQUE_URL");
//            $domain = 'http://52.83.60.235:10271';
            $btns = array();//配置按钮
            $access_token='';//token指定发送到哪个群
            if($value['type'] == 2){ //评论
                $pingbiUrl = $domain."/lunjiu/comment/block?id=".$value['id'];
                $shenheUrl = $domain."/lunjiu/comment/recovery?id=".$value['id'];
                $btns = [
                    ['title' => '屏蔽', 'actionURL' => $pingbiUrl],
                    ['title' => '审核恢复', 'actionURL' => $shenheUrl]
                ];
                $access_token = env('DINGTALK.TOKEN');
            }elseif($value['type'] == 1){//帖子
                if(!empty($value['type_data'])){
                    $imageSoure = explode(',',$value['type_data']);
                    $imageArr = [];
                    foreach ($imageSoure as $vvv){
                        $imageArr[] = env('ALIURL').$vvv;
                    }
                    $value['pics'] = $imageArr;

                }
                $pingbiUrl = $domain."/lunjiu/weibo/block?id=".$value['id'];
                $shenheUrl = $domain."/lunjiu/weibo/recovery?id=".$value['id'];
                $btns = [
                    ['title' => '屏蔽', 'actionURL' => $pingbiUrl],
                    ['title' => '审核恢复', 'actionURL' => $shenheUrl]
                ];
                $access_token = env('DINGTALK.TOKEN');
            }elseif($value['type']==3){
                $yes_pingbi_url=$domain.'/lunjiu/battle/isShield?id='.$value['id'].'&status=1';
                $no_pingbi_url=$domain.'/lunjiu/battle/isShield?id='.$value['id'].'&status=2';
                $btns=[
                    ['title'=>'屏蔽','actionURL'=>$yes_pingbi_url],
                    ['title'=>'解除屏蔽','actionURL'=>$no_pingbi_url]
                ];
                $access_token = env('DINGTALK.TUANZHAN_TOKEN');
            }
            $sendParma = [
                'access_token'=>$access_token,
                'title'=>$nickname,
                'content'=>$value['content'],
                'pics'=>$value['pics']??[],
                'btns'=>$btns
            ];
            $url = env('DINGTALK.IP')."/actioncard";
            Db::name('test')->insert(['content'=>json_encode($sendParma)]);
            //调用推送钉钉接口
            return $microservice->httpPost($url, $sendParma, [],'json');
        }
    }

}

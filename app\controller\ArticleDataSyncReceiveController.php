<?php

namespace app\controller;

use app\model\ArticleModel;
use app\Request;
use app\service\ArticleService;
use think\Db;
use think\exception\ValidateException;
use think\Log;

/**
 * 数据同步
 */
class ArticleDataSyncReceiveController
{

    protected $handleTables= ['vh_article','vh_article_attendcate','vh_article_carousel','vh_article_cate','vh_article_collect','vh_article_comment','vh_article_digg_log','vh_article_log','vh_comment_box','vh_honordy','vh_syn_es'];

    /**
     * 更新数据
     * @param Request $request
     * @return array
     */
    public function handleData(Request $request)
    {
        $param = $request->param();
        if($param['database'] == 'vh_lunjiu' && in_array(@$param['table'],$this->handleTables)){//更新写入条件，vh_lunjiu表、并且在所需要的表范围

            if($param['type'] == "INSERT"){//新增
                $row = Db::name('user')->replace()->insertAll($param['data']);
                if($row>0){
                    return throwResponse([],0,"新增成功，受影响行数：".$row);
                }
                Log::ERROR("同步数据，新增失败。param:".json_encode($param['data']));
                return throwResponse([],-1,"新增失败");
            }

            if($param['type'] == "UPDATE"){//更新
                foreach ($param['data'] as $v){
                    $row = Db::name($param['table'])->save($v);
                    if($row>0){
                        return throwResponse([],0,"修改成功，受影响行数：".$row);
                    }
                    Log::ERROR("同步数据，修改失败。param:".json_encode($v));
                    return throwResponse([],-1,"修改失败");
                }
            }

            return throwResponse([],-1,"该操作类型未授权，请联系管理员");
        }

        return throwResponse([],-1,"不满足更新插入数据条件。");
    }

    /**
     * 接受回调
     * @param Request $request
     * @return bool
     */
    public function DataSyncPool(Request $request)
    {

        #region 验证参数
        $validate = \think\facade\Validate::rule([
            'table|表名'           => 'require',
            'id|id'          => 'require|number|gt:0',
            'status|状态' => 'require|number',
            'viewnums|阅览数' => 'require|number',

        ]);
        $param    = $request->param();
        if (!$validate->check($param)) {
            return throwResponse($validate->getError(),10002);
        }
        #endregion 验证参数
        $result = $this->DataSyncPoolDo($param);
        return throwResponse([]);
    }

    /**
     * 同步数据到推荐池
     * @param $param
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function DataSyncPoolDo($param)
    {

        $field = "id,title,adminid,img,viewnums,status";
        $where = ['id'=>$param['id']];
        $article = ArticleModel::field($field)->where($where)->find();
        if(!$article){
            throw new ValidateException('数据不存在');
        }
        if(!empty($article['img'])){
            $item = [
                'id' => (int)$article['id'],
                'title' => $article['title'],
                'banner_img' => env('ALIURL').$article['img'],
                'user_img' => 'https://images.vinehoo.com/avatars/rabbit.png',
                'user_name' => '酒云小编',
                'pageviews' => (int)$article['viewnums']
            ];
            $is_delete = $article['status'] == 1 ? 0 : 1;
            $info = ['eid' => (int)$article['id'], 'source' => 'news', 'data' => json_encode($item), 'is_delete' => $is_delete];
            $url = env('ITEM.RECOMMEND_URL')."/go-recommend/v3/second/syncPool";//地址
            $result = @http_post_json($url, json_encode($info));

            $result = json_decode($result, true);
            if($result['error_code'] != 0){
                throw new ValidateException('数据同步失败'.$result['error_msg']);
            }
        }
        return true;
    }
}
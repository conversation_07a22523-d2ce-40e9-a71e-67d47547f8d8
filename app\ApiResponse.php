<?php
namespace app;


use app\exception\BusinessException;

trait ApiResponse
{
    /**
     * @var string
     */
    protected $errorCode;
    protected $msg;
    /**
     * @param $errorCode
     * @return $this
     */
    public function setErrorCode($errorCode)
    {
        $this->errorCode = $errorCode;
        return $this;
    }

    /**
     * @param $message
     * @return mixed
     */
    public function setMessage($message)
    {
        $this->msg = $message;
        return $this;
    }


    /**
     * @param $status
     * @param array $data
     * @param string $errorCode
     * @return mixed
     */
    public function status($data = [], $status = 'success',  $errorCode = 0)
    {
        $this->setErrorCode($errorCode);

        $result = [
            //'status'=> $status,
            'error_code'   => $this->errorCode,
            'error_msg'    => empty($this->msg) ? config('errorCode')[$this->errorCode] : $this->msg,
            'data'    =>empty($data) && is_array($data) ? (object)[] : $data
        ];

        return json($result,200);
    }


    /**
     * @param string $message
     * @param string $errorCode
     * @return mixed
     */
    public function failed($msg,$errorCode = '-1')
    {
        return $this->setMessage($msg)->status([],'fail',$errorCode);
    }

    /**
     * @param $data
     * @param int $status
     * @return mixed
     */
    public function success($data = [],$status = 'success')
    {
        return $this->status($data,$status);
    }


    public function throwError($msg = "",$errorCode = -1,$code = 200) {

        $data = [
            //'status'=> 'fail',
            'error_msg' =>empty($msg) ? config('errorCode')[$errorCode] : $msg,
            'msg' =>empty($msg) ? config('errorCode')[$errorCode] : $msg,
            'error_code'=>$errorCode,
            'errorCode'=>$errorCode,
            'code' =>$code
        ];

        throw new BusinessException($data);
    }

}
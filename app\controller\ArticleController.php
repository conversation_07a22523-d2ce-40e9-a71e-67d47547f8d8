<?php

namespace app\controller;

use app\BaseController;
use app\Request;
use app\service\admin\ArticleService;
use think\facade\Validate;

/**
 * 后台管理--酒闻管理
 */
class ArticleController extends BaseController
{
    /**
     * @OA\Post(path="/lunjiu/admin/article/getArticleList",
     *   tags={"酒闻后台管理"},
     *   summary="酒闻列表",
     *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
     *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
     *   @OA\RequestBody(
     *     @OA\MediaType(
     *       mediaType="multipart/form-data",
     *         @OA\Schema(
     *               @OA\Property(description="资讯分类", property="cate_id", type="integer", default=""),
     *               @OA\Property(description="状态 1已审核 2未审核", property="status", type="integer", default=""),
     *               @OA\Property(description="排序 1浏览量 2状态 3更新时间", property="stort", type="integer", default=""),
     *               @OA\Property(description="关键字", property="keywords", type="string", default=""),
     *               @OA\Property(description="资讯ID", property="id", type="integer", default=""),
     *               @OA\Property(description="每页显示条数（不能超过30条）", property="pagesize", type="integer", default="10"),
     *               @OA\Property(description="页码", property="pagenumber", type="integer", default="1"),
     *           )
     *       )
     *     ),
     *    @OA\Response(
     *            response=200,
     *            description="接口请求成功",
     *            @OA\MediaType(
     *                mediaType="application/json",
     *            @OA\Schema(
     *                 @OA\Property(property="status",
     *                    type="string",
     *                    example="success/fail",
     *                    description="状态"
     *                ),
     *                 @OA\Property(property="errorCode",
     *                    type="string",
     *                    example="0/-1",
     *                    description="状态编码"
     *                ),
     *                 @OA\Property(property="msg",
     *                    type="string",
     *                    example="ok",
     *                    description="状态描述"
     *                ),
     *                @OA\Property(property="data",
     *                   type="object",description="数据",
     *                   @OA\Property(property="list",type="array",description="酒闻数据",
     *                       @OA\Items(
     *                           type="object",description="",
     *                           @OA\Property(property="id",type="integer",example="1",description="资讯id"),
     *                           @OA\Property(property="catename",type="string",example="毛毛虫化蝶传记",description="文章分类名称"),
     *                           @OA\Property(property="adname",type="string",example="毛毛虫化蝶传记",description="管理员名称"),
     *                           @OA\Property(property="title",type="string",example="毛毛虫化蝶传记",description="文章标题"),
     *                           @OA\Property(property="img",type="string",example="/image.jpg",description="文章缩略图"),
     *                           @OA\Property(property="updatetime",type="string",example="哇呵呵呵呵",description="更新时间"),
     *                           @OA\Property(property="viewnums",type="integer",example="20",description="浏览次数"),
     *                           @OA\Property(property="ordid",type="integer",example="20",description="排序"),
     *                           @OA\Property(property="is_hot",type="integer",example="10",description="是否热门 1是 0否"),
     *                           @OA\Property(property="is_index",type="integer",example="10",description="是否首页显示 1是 0否（推荐）"),
     *                           @OA\Property(property="status",type="integer",example="10",description="文章状态 0-待审核 1-已审核"),
     *                       )
     *                   ),
     *                   @OA\Property(property="totalPage",type="string",example="20",description="总页数"),
     *                   @OA\Property(property="nowPage",type="integer",example="1",description="当前页"),
     *                   @OA\Property(property="totalNum",type="integer",example="50",description="数据总条数"),
     *                   @OA\Property(property="pagesize",type="integer",example="10",description="每页显示条数"),
     *               ),
     *            )
     *        ),
     *    )
     * )
     */
    public function getArticleList(Request $request)
    {
        $param = $request->param();
        //$header = $request->header();
        //$is_true = isset($header['securitycheckval']);//判断是否存在token参数
        //$header['securitycheckval'] = $is_true?$header['securitycheckval']:'';//token值
        $ArticleService = new ArticleService();
        $is_res         = $ArticleService->getAdminArticleList($param, "");
        return $this->success($is_res);
    }


    /**
     * @OA\Post(path="/lunjiu/admin/article/operateArticle",
     *   tags={"酒闻后台管理"},
     *   summary="添加/编辑酒闻",
     *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
     *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
     *   @OA\RequestBody(
     *     @OA\MediaType(
     *       mediaType="multipart/form-data",
     *         @OA\Schema(
     *               @OA\Property(description="标题名称", property="title", type="string", default=""),
     *               @OA\Property(description="所属分类", property="cate_id", type="integer", default=""),
     *               @OA\Property(description="国家", property="country", type="string", default=""),
     *               @OA\Property(description="图片", property="img", type="string", default=""),
     *               @OA\Property(description="摘要简介", property="abst", type="text", default=""),
     *               @OA\Property(description="详细内容", property="info", type="text", default="10"),
     *               @OA\Property(description="关联国家ID", property="countrys", type="string", default="1"),
     *               @OA\Property(description="关联酒庄ID", property="villages", type="string", default="1"),
     *               @OA\Property(description="关联产区ID", property="areas", type="string", default="1"),
     *               @OA\Property(description="关联酒款ID", property="wids", type="string", default="1"),
     *               @OA\Property(description="排序", property="ordid", type="integer", default="1"),
     *               @OA\Property(description="是否热门 1是 0否", property="is_hot", type="integer", default="1"),
     *               @OA\Property(description="是否推荐 1是 0否", property="is_index", type="integer", default="1"),
     *               @OA\Property(description="上线状态", property="status", type="integer", default="1"),
     *               @OA\Property(description="作者/来源", property="source", type="string", default="1"),
     *               @OA\Property(description="文章id（编辑必传）", property="id", type="integer", default=""),
     *           required={"title","cate_id","abst","info"})
     *       )
     *     ),
     *    @OA\Response(
     *            response=200,
     *            description="接口请求成功",
     *            @OA\MediaType(
     *                mediaType="application/json",
     *            @OA\Schema(
     *                 @OA\Property(property="status",
     *                    type="string",
     *                    example="success/fail",
     *                    description="状态"
     *                ),
     *                 @OA\Property(property="errorCode",
     *                    type="string",
     *                    example="0/-1",
     *                    description="状态编码"
     *                ),
     *                 @OA\Property(property="msg",
     *                    type="string",
     *                    example="ok",
     *                    description="状态描述"
     *                ),
     *                @OA\Property(property="data",
     *                   type="object",description="数据",
     *               ),
     *            )
     *        ),
     *    )
     * )
     */
    public function operateArticle(Request $request)
    {
        $param                      = $request->param();
        $header                     = $request->header();
        $is_true                    = isset($header['securitycheckval']);//判断是否存在token参数
        $header['securitycheckval'] = $is_true ? $header['securitycheckval'] : '';//token值

        if (!is_int($param['ordid'])) return $this->failed("排序类型为整数,请确认。", -1);
//        exit();
        $ArticleService = new ArticleService();
        $is_res         = $ArticleService->operateArticle($param, $header);

        if ($is_res['flag'] == 1) {
            return $this->success($is_res['msg']);
        } else {
            return $this->failed($is_res['msg'], $is_res['flag']);
        }
    }


    /**
     * @OA\Post(path="/lunjiu/admin/article/changeArticleStatus",
     *   tags={"酒闻后台管理"},
     *   summary="更改酒闻状态",
     *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
     *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
     *   @OA\RequestBody(
     *     @OA\MediaType(
     *       mediaType="multipart/form-data",
     *         @OA\Schema(
     *               @OA\Property(description="文章id", property="id", type="integer", default=""),
     *               @OA\Property(description="类型 1：热搜 2：推荐 3：上线状态", property="type", type="integer", default=""),
     *               @OA\Property(description="状态 1：是 0：否", property="status", type="integer", default=""),
     *           required={"id","type","status"})
     *       )
     *     ),
     *    @OA\Response(
     *            response=200,
     *            description="接口请求成功",
     *            @OA\MediaType(
     *                mediaType="application/json",
     *            @OA\Schema(
     *                 @OA\Property(property="status",
     *                    type="string",
     *                    example="success/fail",
     *                    description="状态"
     *                ),
     *                 @OA\Property(property="errorCode",
     *                    type="string",
     *                    example="0/-1",
     *                    description="状态编码"
     *                ),
     *                 @OA\Property(property="msg",
     *                    type="string",
     *                    example="ok",
     *                    description="状态描述"
     *                ),
     *                @OA\Property(property="data",
     *                   type="object",description="数据",
     *               ),
     *            )
     *        ),
     *    )
     * )
     */
    public function changeArticleStatus(Request $request)
    {
        $param                      = $request->param();
        $header                     = $request->header();
        $is_true                    = isset($header['securitycheckval']);//判断是否存在token参数
        $header['securitycheckval'] = $is_true ? $header['securitycheckval'] : '';//token值
        $ArticleService             = new ArticleService();
        $is_res                     = $ArticleService->changeArticleStatus($param, $header);

        if ($is_res['flag'] == 1) {
            return $this->success($is_res['msg']);
        } else {
            return $this->failed($is_res['msg'], $is_res['flag']);
        }
    }


    /**
     * @OA\Post(path="/lunjiu/admin/article/getArticleInfo",
     *   tags={"酒闻后台管理"},
     *   summary="酒闻详情",
     *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
     *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
     *   @OA\RequestBody(
     *     @OA\MediaType(
     *       mediaType="multipart/form-data",
     *         @OA\Schema(
     *               @OA\Property(description="文章id", property="id", type="integer", default=""),
     *           required={"id"})
     *       )
     *     ),
     *    @OA\Response(
     *            response=200,
     *            description="接口请求成功",
     *            @OA\MediaType(
     *                mediaType="application/json",
     *            @OA\Schema(
     *                 @OA\Property(property="status",
     *                    type="string",
     *                    example="success/fail",
     *                    description="状态"
     *                ),
     *                 @OA\Property(property="errorCode",
     *                    type="string",
     *                    example="0/-1",
     *                    description="状态编码"
     *                ),
     *                 @OA\Property(property="msg",
     *                    type="string",
     *                    example="ok",
     *                    description="状态描述"
     *                ),
     *                @OA\Property(property="data",
     *                   type="object",description="数据",
     *                   @OA\Property(property="list",type="array",description="酒闻数据",
     *                       @OA\Items(
     *                           type="object",description="",
     *                           @OA\Property(property="id",type="integer",example="1",description="资讯id"),
     *                           @OA\Property(property="cate_id",type="integer",example="1",description="文章分类id"),
     *                           @OA\Property(property="title",type="string",example="毛毛虫化蝶传记",description="标题"),
     *                           @OA\Property(property="country",type="string",example="1",description="国家"),
     *                           @OA\Property(property="img",type="string",example="/image.jpg",description="文章缩略图"),
     *                           @OA\Property(property="abst",type="string",example="哇呵呵呵呵",description="副标题"),
     *                           @OA\Property(property="info",type="string",example="",description="文章内容"),
     *                           @OA\Property(property="ordid",type="integer",example="",description="排序"),
     *                           @OA\Property(property="is_hot",type="integer",example="",description="排序"),
     *                           @OA\Property(property="is_index",type="integer",example="",description="是否首页显示（推荐） 1是 0否"),
     *                           @OA\Property(property="status",type="integer",example="",description="文章状态（上线状态） 0-待审核 1-已审核"),
     *                           @OA\Property(property="wids",type="string",example="",description="酒款ID"),
     *                           @OA\Property(property="countrys",type="string",example="",description="国家ID"),
     *                           @OA\Property(property="villages",type="string",example="",description="来源城市"),
     *                           @OA\Property(property="areas",type="string",example="",description="来源地区"),
     *                           @OA\Property(property="source",type="string",example="",description="文章来源"),
     *                       )
     *                   ),
     *               ),
     *            )
     *        ),
     *    )
     * )
     */
    public function getArticleInfo(Request $request)
    {
        $param                      = $request->param();
        $header                     = $request->header();
        $is_true                    = isset($header['securitycheckval']);//判断是否存在token参数
        $header['securitycheckval'] = $is_true ? $header['securitycheckval'] : '';//token值
        $ArticleService             = new ArticleService();
        $is_res                     = $ArticleService->getArticleInfo($param, $header);

        return $this->success($is_res);
    }


    /**
     * @OA\Post(path="/lunjiu/admin/article/delArticle",
     *   tags={"酒闻后台管理"},
     *   summary="删除酒闻",
     *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
     *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
     *   @OA\RequestBody(
     *     @OA\MediaType(
     *       mediaType="multipart/form-data",
     *         @OA\Schema(
     *               @OA\Property(description="文章id（批量删除是id英文逗号分隔的字符串）", property="id", type="string", default=""),
     *           required={"id"})
     *       )
     *     ),
     *    @OA\Response(
     *            response=200,
     *            description="接口请求成功",
     *            @OA\MediaType(
     *                mediaType="application/json",
     *            @OA\Schema(
     *                 @OA\Property(property="status",
     *                    type="string",
     *                    example="success/fail",
     *                    description="状态"
     *                ),
     *                 @OA\Property(property="errorCode",
     *                    type="string",
     *                    example="0/-1",
     *                    description="状态编码"
     *                ),
     *                 @OA\Property(property="msg",
     *                    type="string",
     *                    example="ok",
     *                    description="状态描述"
     *                ),
     *                @OA\Property(property="data",
     *                   type="object",description="数据",
     *               ),
     *            )
     *        ),
     *    )
     * )
     */
    public function delArticle(Request $request)
    {
        $param                      = $request->param();
        $header                     = $request->header();
        $is_true                    = isset($header['securitycheckval']);//判断是否存在token参数
        $header['securitycheckval'] = $is_true ? $header['securitycheckval'] : '';//token值
        $ArticleService             = new ArticleService();
        $is_res                     = $ArticleService->delArticle($param, $header);

        if ($is_res['flag'] == 1) {
            return $this->success($is_res['msg']);
        } else {
            return $this->failed($is_res['msg'], $is_res['flag']);
        }
    }


    /**
     * @OA\Post(path="/lunjiu/admin/article/articleSort",
     *   tags={"酒闻后台管理"},
     *   summary="排序",
     *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
     *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
     *   @OA\RequestBody(
     *     @OA\MediaType(
     *       mediaType="multipart/form-data",
     *         @OA\Schema(
     *              @OA\Property(property="id",type="integer",example="1",description="酒闻id"),
     *              @OA\Property(property="ordid",type="integer",example="1",description="排序"),
     *           required={"id","ordid"})
     *       )
     *     ),
     *   @OA\Response(
     *         response=200,
     *         description="接口请求成功",
     *         @OA\MediaType(
     *         mediaType="application/json",
     *         @OA\Schema(
     *             @OA\Property(property="status",
     *                 type="string",
     *                 example="ok/fail",
     *                 description="状态"
     *             ),
     *             @OA\Property(property="errorCode",
     *                 type="string",
     *                 example="0/-1",
     *                 description="状态编码"
     *             ),
     *             @OA\Property(property="msg",
     *                 type="string",
     *                 example="ok",
     *                 description="状态描述"
     *             ),
     *             @OA\Property(property="data",
     *             type="object",description="返回参数",
     *                   ),
     *               ),
     *            )
     *         ),
     *     )
     * )
     */
    public function articleSort(Request $request)
    {
        $param                      = $request->param();
        $header                     = $request->header();
        $is_true                    = isset($header['securitycheckval']);//判断是否存在token参数
        $header['securitycheckval'] = $is_true ? $header['securitycheckval'] : '';//token值
        $ArticleService             = new ArticleService();
        $is_res                     = $ArticleService->articleSort($param, $header);

        if ($is_res['flag'] == 1) {
            return $this->success($is_res['msg']);
        } else {
            return $this->failed($is_res['msg'], $is_res['flag']);
        }
    }


    /**
     * @OA\Post(path="/lunjiu/article/getArticleList",
     *   tags={"酒闻资讯"},
     *   summary="酒闻列表",
     *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
     *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
     *   @OA\RequestBody(
     *     @OA\MediaType(
     *       mediaType="multipart/form-data",
     *         @OA\Schema(
     *               @OA\Property(description="每页显示条数（不能超过30条）", property="pagesize", type="integer", default="10"),
     *               @OA\Property(description="页码", property="pagenumber", type="integer", default="1"),
     *               @OA\Property(description="唯一uuid/设备号", property="uuid", type="string", default="521555"),
     *               required={"uuid"})
     *       )
     *     ),
     *    @OA\Response(
     *            response=200,
     *            description="接口请求成功",
     *            @OA\MediaType(
     *                mediaType="application/json",
     *            @OA\Schema(
     *                 @OA\Property(property="status",
     *                    type="string",
     *                    example="success/fail",
     *                    description="状态"
     *                ),
     *                 @OA\Property(property="errorCode",
     *                    type="string",
     *                    example="0/-1",
     *                    description="状态编码"
     *                ),
     *                 @OA\Property(property="msg",
     *                    type="string",
     *                    example="ok",
     *                    description="状态描述"
     *                ),
     *                @OA\Property(property="data",
     *                   type="object",description="数据",
     *                   @OA\Property(property="list",type="array",description="酒闻数据",
     *                       @OA\Items(
     *                           type="object",description="",
     *                           @OA\Property(property="id",type="integer",example="1",description="资讯id"),
     *                           @OA\Property(property="title",type="string",example="毛毛虫化蝶传记",description="标题"),
     *                           @OA\Property(property="img",type="string",example="/image.jpg",description="文章缩略图"),
     *                           @OA\Property(property="abst",type="string",example="哇呵呵呵呵",description="副标题"),
     *                           @OA\Property(property="viewnums",type="string",example="20",description="浏览次数"),
     *                           @OA\Property(property="commentnums",type="string",example="10",description="评论数"),
     *                       )
     *                   ),
     *                   @OA\Property(property="totalPage",type="string",example="20",description="总页数"),
     *                   @OA\Property(property="nowPage",type="integer",example="1",description="当前页"),
     *                   @OA\Property(property="totalNum",type="integer",example="50",description="数据总条数"),
     *                   @OA\Property(property="pagesize",type="integer",example="10",description="每页显示条数"),
     *               ),
     *            )
     *        ),
     *    )
     * )
     */
    public function getIndexArticleList(Request $request)
    {
        $param  = $request->param();
        $header = $request->header();
        //$is_true = isset($header['securitycheckval']);//判断是否存在token参数
        //$header['securitycheckval'] = $is_true?$header['securitycheckval']:'';//token值
        $ArticleService = new \app\service\ArticleService();
        //$is_res = $ArticleService->getArticleList($param,$header);
        $is_res = $ArticleService->getArticleList($param, $header);

        if ($is_res['flag'] == 1) {
            return $this->success($is_res['data']);
        } else {
            return $this->failed($is_res['msg'], $is_res['flag']);
        }
    }


    /**
     * @OA\Get(path="/lunjiu/article/getArticleDetails",
     *   tags={"酒闻资讯"},
     *   summary="酒闻详情",
     *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
     *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
     *   @OA\Parameter(name="id", in="query", description="资讯id",required=true,@OA\Schema(type="integer", default="872")),
     *   @OA\Parameter(name="uuid", in="query", description="唯一uuid/设备号",required=true,@OA\Schema(type="string", default="521555")),
     *    @OA\Response(
     *            response=200,
     *            description="接口请求成功",
     *            @OA\MediaType(
     *                mediaType="application/json",
     *            @OA\Schema(
     *                 @OA\Property(property="status",
     *                    type="string",
     *                    example="success/fail",
     *                    description="状态"
     *                ),
     *                 @OA\Property(property="errorCode",
     *                    type="string",
     *                    example="0/-1",
     *                    description="状态编码"
     *                ),
     *                 @OA\Property(property="msg",
     *                    type="string",
     *                    example="ok",
     *                    description="状态描述"
     *                ),
     *                @OA\Property(property="data",
     *                   type="object",description="数据",
     *                   @OA\Property(property="list",type="array",description="酒闻数据",
     *                       @OA\Items(
     *                           type="object",description="",
     *                           @OA\Property(property="id",type="integer",example="1",description="资讯id"),
     *                           @OA\Property(property="title",type="string",example="毛毛虫化蝶传记",description="标题"),
     *                           @OA\Property(property="img",type="string",example="/image.jpg",description="文章缩略图"),
     *                           @OA\Property(property="abst",type="string",example="哇呵呵呵呵",description="副标题"),
     *                           @OA\Property(property="viewnums",type="string",example="20",description="浏览次数"),
     *                           @OA\Property(property="commentnums",type="string",example="10",description="评论数"),
     *                           @OA\Property(property="info",type="string",example="哇呵呵呵呵",description="资讯内容"),
     *                           @OA\Property(property="catename",type="string",example="知识",description="资讯标签名称"),
     *                           @OA\Property(property="is_collect",type="integer",example="1",description="是否收藏 1已收藏 0未收藏（未登录默认未收藏）"),
     *                           @OA\Property(property="add_time",type="string",example="添加时间",description="2020-06-01 11:12:22"),
     *                           @OA\Property(property="share_link",type="string",example="1",description="分享地址"),
     *                           @OA\Property(property="detail_link",type="string",example="1",description="酒闻详情地址"),
     *                       )
     *                   ),
     *               ),
     *            )
     *        ),
     *    )
     * )
     */
    public function getIndexArticleDetails(Request $request)
    {
        $param  = $request->param();
        $header = $request->header();
        if (!isset($param['id']) || empty($param['id'])) {
            return $this->failed('资讯id不能为空');
        }
//        $is_true = isset($header['securitycheckval']);//判断是否存在token参数
//        $header['securitycheckval'] = $is_true?$header['securitycheckval']:'';//token值
        $ArticleService = new ArticleService();
        $is_res         = $ArticleService->getArticleDetails($param, $header);

        if ($is_res['flag'] == 1) {
            return $this->success($is_res['data']);
        } else {
            return $this->failed($is_res['msg'], $is_res['flag']);
        }
    }

    /**
     * 查询用户是否收藏
     * @param Request $request
     * @return mixed
     */
    public function getIsCollect(Request $request)
    {
        $param        = $request->param();
        $param['uid'] = $request->header('vinehoo-uid');
        if (empty($param['id'])) {
            return $this->failed("酒闻id必传");
        }
        $articleService = new ArticleService();
        $result         = $articleService->getIsCollect($param);
        return $this->success($result);

    }

    /**
     * 酒闻生成js
     * @param Request $request
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getArticleCreateJs(Request $request)
    {
        $ArticleService = new ArticleService();
        $result         = $ArticleService->createArticleJs();
        return $this->success($result);
    }

    /**
     * @OA\Post(path="/lunjiu/article/getArticleShareUrl",
     *   tags={"酒闻资讯"},
     *   summary="酒闻分享",
     *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
     *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
     *   @OA\RequestBody(
     *     @OA\MediaType(
     *       mediaType="multipart/form-data",
     *           @OA\Schema(
     *               @OA\Property(description="资讯id", property="id", type="integer", default="872"),
     *               @OA\Property(description="唯一uuid/设备号", property="uuid", type="string", default="521555"),
     *               required={"id","uuid"})
     *       )
     *     ),
     *    @OA\Response(
     *            response=200,
     *            description="接口请求成功",
     *            @OA\MediaType(
     *                mediaType="application/json",
     *            @OA\Schema(
     *                 @OA\Property(property="status",
     *                    type="string",
     *                    example="success/fail",
     *                    description="状态"
     *                ),
     *                 @OA\Property(property="errorCode",
     *                    type="string",
     *                    example="0/-1",
     *                    description="状态编码"
     *                ),
     *                 @OA\Property(property="msg",
     *                    type="string",
     *                    example="ok",
     *                    description="状态描述"
     *                ),
     *                @OA\Property(property="data",
     *                   type="object",description="数据",
     *                   @OA\Property(property="share",type="string",description="分享地址",example="10")
     *                   ),
     *               ),
     *            )
     *        ),
     *    )
     * )
     */
    public function getIndexArticleShareUrl(Request $request)
    {
        $param = $request->param();
        //$header = $request->header();
        //$is_true = isset($header['securitycheckval']);//判断是否存在token参数
        //$header['securitycheckval'] = $is_true?$header['securitycheckval']:'';//token值

        //$is_res = Config('config')['WEIBO_URL']."/lunjiu/article/getArticleDetails?id={$param['id']}&uuid={$param['uuid']}";
        //$is_res = Config('config')['WEIBO_URL']."/jiuwenindex/v3/article/getArticleDetails?id={$param['id']}";
        $is_res['share'] = env('ITEM.NEWS_URL') . "/jiuwenindex/v3/article/getArticleDetails?id={$param['id']}";

        return $this->success($is_res);
    }


    /**
     * @OA\Post(path="/lunjiu/article/getArticleCollect",
     *   tags={"酒闻资讯"},
     *   summary="酒闻收藏",
     *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
     *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
     *   @OA\RequestBody(
     *     @OA\MediaType(
     *       mediaType="multipart/form-data",
     *           @OA\Schema(
     *               @OA\Property(description="资讯id", property="id", type="integer", default="872"),
     *               @OA\Property(description="类型 0取消收藏 1收藏", property="status", type="integer", default="1"),
     *               @OA\Property(description="唯一uuid/设备号", property="uuid", type="string", default="521555"),
     *               required={"id","status","uuid"})
     *       )
     *     ),
     *    @OA\Response(
     *            response=200,
     *            description="接口请求成功",
     *            @OA\MediaType(
     *                mediaType="application/json",
     *            @OA\Schema(
     *                 @OA\Property(property="status",
     *                    type="string",
     *                    example="success/fail",
     *                    description="状态"
     *                ),
     *                 @OA\Property(property="errorCode",
     *                    type="string",
     *                    example="0/-1",
     *                    description="状态编码"
     *                ),
     *                 @OA\Property(property="msg",
     *                    type="string",
     *                    example="ok",
     *                    description="状态描述"
     *                ),
     *                @OA\Property(property="data",
     *                   type="object",description="数据",
     *                   ),
     *               ),
     *            )
     *        ),
     *    )
     * )
     */
    public function getIndexArticleCollect(Request $request)
    {
        $param  = $request->param();
        $header = $request->header();
        //$is_true = isset($header['securitycheckval']);//判断是否存在token参数
        //$header['securitycheckval'] = $is_true?$header['securitycheckval']:'';//token值
        $ArticleService = new ArticleService();
        $is_res         = $ArticleService->getArticleCollect($param, $header);

        if ($is_res['flag'] == 1) {
            return $this->success($is_res['msg']);
        } else {
            return $this->failed($is_res['msg'], $is_res['flag']);
        }
    }


    public function getMyArticleCollectCounts(Request $request)
    {
        $param = $request->param();
        if (!isset($param['uid'])) return throwResponse([], -1, "uid不能为空");
        $ArticleService = new ArticleService();
        $result         = $ArticleService->getMyArticleCollectCounts($param);
        return throwResponse($result);

    }


    /**
     * @OA\Post(path="/lunjiu/article/getArticleComment",
     *   tags={"酒闻资讯"},
     *   summary="资讯评论列表",
     *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
     *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
     *   @OA\RequestBody(
     *     @OA\MediaType(
     *       mediaType="multipart/form-data",
     *           @OA\Schema(
     *               @OA\Property(description="酒闻id", property="id", type="integer", default="872"),
     *               @OA\Property(description="每页显示条数（不能超过30条）", property="pagesize", type="integer", default="10"),
     *               @OA\Property(description="页码", property="pagenumber", type="integer", default="1"),
     *               @OA\Property(description="唯一uuid/设备号", property="uuid", type="string", default="521555"),
     *               required={"id","uuid"})
     *       )
     *     ),
     *    @OA\Response(
     *            response=200,
     *            description="接口请求成功",
     *            @OA\MediaType(
     *                mediaType="application/json",
     *            @OA\Schema(
     *                 @OA\Property(property="status",
     *                    type="string",
     *                    example="success/fail",
     *                    description="状态"
     *                ),
     *                 @OA\Property(property="errorCode",
     *                    type="string",
     *                    example="0/-1",
     *                    description="状态编码"
     *                ),
     *                 @OA\Property(property="msg",
     *                    type="string",
     *                    example="ok",
     *                    description="状态描述"
     *                ),
     *                @OA\Property(property="data",
     *                   type="object",description="数据",
     *                   @OA\Property(property="list",type="array",description="评论数据",
     *                       @OA\Items(
     *                           type="object",description="",
     *                           @OA\Property(property="comid",type="string",example="1",description="评论id"),
     *                           @OA\Property(property="uid",type="integer",example="1",description="发布评论的用户id"),
     *                           @OA\Property(property="content",type="string",example="上海",description="评论内容"),
     *                           @OA\Property(property="addtime",type="string",example="2020-01-20 20:10:10",description="评论时间"),
     *                           @OA\Property(property="address",type="string",example="上海",description="位置"),
     *                           @OA\Property(property="diggnums",type="integer",example="10",description="赞次数"),
     *                           @OA\Property(property="is_digg",type="integer",example="1",description="是否可以点赞 1是 0否(未登录默认为可以点赞)"),
     *                           @OA\Property(property="userinfo",type="array",description="资讯评论用户信息",
     *                               @OA\Items(
     *                                   type="object",description="",
     *                                   @OA\Property(property="nickname",type="string",example="10",description="昵称"),
     *                                   @OA\Property(property="avatar_image",type="string",example="10",description="头像"),
     *                                   @OA\Property(property="ulevel",type="int",example="10",description="用户等级"),
     *                                   @OA\Property(property="certification_level",type="string",example="WSET3",description="认证等级"),
     *                               ),
     *                           ),
     *                           @OA\Property(property="replyCommentInfo",type="array",description="回复评论信息",
     *                               @OA\Items(
     *                                   type="object",description="",
     *                                   @OA\Property(property="comid",type="string",example="1",description="评论id"),
     *                                   @OA\Property(property="uid",type="integer",example="1",description="发布评论的用户id"),
     *                                   @OA\Property(property="content",type="string",example="上海",description="评论内容"),
     *                                   @OA\Property(property="addtime",type="string",example="2020-01-20 20:10:10",description="评论时间"),
     *                                   @OA\Property(property="address",type="string",example="上海",description="位置"),
     *                                   @OA\Property(property="diggnums",type="integer",example="10",description="赞次数"),
     *                                   @OA\Property(property="recomid",type="integer",example="10",description="被评论的评论id"),
     *                                   @OA\Property(property="article_username",type="string",example="毛毛虫",description="被评论的评论用户昵称"),
     *                                   @OA\Property(property="userinfo",type="array",description="回复评论用户信息",
     *                                       @OA\Items(
     *                                           type="object",description="",
     *                                           @OA\Property(property="nickname",type="string",example="10",description="昵称"),
     *                                           @OA\Property(property="avatar_image",type="string",example="10",description="头像"),
     *                                           @OA\Property(property="ulevel",type="int",example="10",description="用户等级"),
     *                                           @OA\Property(property="certification_level",type="string",example="WSET3",description="认证等级"),
     *                                       ),
     *                                   ),
     *                               ),
     *                           ),
     *                       ),
     *                   ),
     *                   @OA\Property(property="totalPage",type="string",example="10",description="总页数"),
     *                   @OA\Property(property="nowPage",type="integer",example="1",description="当前页"),
     *                   @OA\Property(property="totalNum",type="integer",example="100",description="总条数"),
     *                   @OA\Property(property="limit",type="integer",example="10",description="分页限制"),
     *               ),
     *            )
     *        ),
     *    )
     * )
     */
    public function getIndexArticleComment(Request $request)
    {
        $param  = $request->param();
        $header = $request->header();
//        $is_true = isset($header['securitycheckval']);//判断是否存在token参数
//        $header['securitycheckval'] = $is_true?$header['securitycheckval']:'';//token值
        $ArticleService = new ArticleService();
        $is_res         = $ArticleService->getArticleComment($param, $header);

        if ($is_res['flag'] == 1) {
            return $this->success($is_res['data']);
        } else {
            return $this->failed($is_res['msg'], $is_res['flag']);
        }
    }


    /**
     * @OA\Post(path="/lunjiu/article/doDigg",
     *   tags={"酒闻资讯"},
     *   summary="资讯、评论点赞",
     *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
     *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
     *   @OA\RequestBody(
     *     @OA\MediaType(
     *       mediaType="multipart/form-data",
     *           @OA\Schema(
     *               @OA\Property(description="点赞信息id（资讯或评论id）", property="id", type="integer", default="872"),
     *               @OA\Property(description="点赞类型 1资讯 2评论", property="type", type="integer", default="1"),
     *               @OA\Property(description="唯一uuid/设备号", property="uuid", type="string", default="521555"),
     *               required={"id","uuid","type"})
     *       )
     *     ),
     *    @OA\Response(
     *            response=200,
     *            description="接口请求成功",
     *            @OA\MediaType(
     *                mediaType="application/json",
     *            @OA\Schema(
     *                 @OA\Property(property="status",
     *                    type="string",
     *                    example="success/fail",
     *                    description="状态"
     *                ),
     *                 @OA\Property(property="errorCode",
     *                    type="string",
     *                    example="0/-1",
     *                    description="状态编码"
     *                ),
     *                 @OA\Property(property="msg",
     *                    type="string",
     *                    example="ok",
     *                    description="状态描述"
     *                ),
     *                @OA\Property(property="data",
     *                   type="object",description="数据",
     *
     *               ),
     *            ),
     *            )
     *        ),
     *    )
     * )
     */
    public function doIndexDigg(Request $request)
    {
        $param  = $request->param();
        $header = $request->header();

        if (!isset($header['vinehoo-uid'])) {
            return $this->failed("请先登录", -1);
        }

        $ArticleService = new ArticleService();
        $is_res         = $ArticleService->doDigg($param, $header);

        if ($is_res['flag'] == 1) {
            return $this->success($is_res['msg']);
        } else {
            return $this->failed($is_res['msg'], $is_res['flag']);
        }
    }

    /**
     * Description:酒闻资讯--资讯、评论取消点赞
     * Author: zrc
     * Date: 2023/7/6
     * Time: 11:19
     * @param Request $request
     * @return mixed
     */
    public function cancelLike(Request $request)
    {
        $params        = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'id|数据ID' => 'require|number',
            'type|类型' => 'require|in:1,2',
        ]);
        if (!$validate->check($params)) {
            $this->failed($validate->getError(), 10001);
        }
        $ArticleService = new ArticleService();
        $result         = $ArticleService->cancelLike($params);
        return $this->success($result);
    }


    /**
     * @OA\Post(path="/lunjiu/article/getMytArticleCollectList",
     *   tags={"酒闻资讯"},
     *   summary="酒闻收藏列表",
     *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
     *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
     *   @OA\RequestBody(
     *     @OA\MediaType(
     *       mediaType="multipart/form-data",
     *         @OA\Schema(
     *               @OA\Property(description="每页显示条数（不能超过30条）", property="pagesize", type="integer", default="10"),
     *               @OA\Property(description="页码", property="pagenumber", type="integer", default="1"),
     *               @OA\Property(description="唯一uuid/设备号", property="uuid", type="string", default="521555"),
     *               required={"uuid"})
     *       )
     *     ),
     *    @OA\Response(
     *            response=200,
     *            description="接口请求成功",
     *            @OA\MediaType(
     *                mediaType="application/json",
     *            @OA\Schema(
     *                 @OA\Property(property="status",
     *                    type="string",
     *                    example="success/fail",
     *                    description="状态"
     *                ),
     *                 @OA\Property(property="errorCode",
     *                    type="string",
     *                    example="0/-1",
     *                    description="状态编码"
     *                ),
     *                 @OA\Property(property="msg",
     *                    type="string",
     *                    example="ok",
     *                    description="状态描述"
     *                ),
     *                @OA\Property(property="data",
     *                   type="object",description="数据",
     *                   @OA\Property(property="list",type="array",description="酒闻数据",
     *                       @OA\Items(
     *                           type="object",description="",
     *                           @OA\Property(property="id",type="integer",example="1",description="资讯id"),
     *                           @OA\Property(property="title",type="string",example="毛毛虫化蝶传记",description="标题"),
     *                           @OA\Property(property="img",type="string",example="/image.jpg",description="文章缩略图"),
     *                           @OA\Property(property="abst",type="string",example="哇呵呵呵呵",description="副标题"),
     *                           @OA\Property(property="viewnums",type="string",example="20",description="浏览次数"),
     *                           @OA\Property(property="commentnums",type="string",example="10",description="评论数"),
     *                       )
     *                   ),
     *                   @OA\Property(property="totalPage",type="string",example="20",description="总页数"),
     *                   @OA\Property(property="nowPage",type="integer",example="1",description="当前页"),
     *                   @OA\Property(property="totalNum",type="integer",example="50",description="数据总条数"),
     *                   @OA\Property(property="pagesize",type="integer",example="10",description="每页显示条数"),
     *               ),
     *            )
     *        ),
     *    )
     * )
     */
    public function getIndexMyArticleCollectList(Request $request)
    {
        $param  = $request->param();
        $header = $request->header();
        //$is_true = isset($header['securitycheckval']);//判断是否存在token参数
        //$header['securitycheckval'] = $is_true?$header['securitycheckval']:'';//token值
        $ArticleService = new ArticleService();
        $is_res         = $ArticleService->getMyArticleCollectList($param, $header);

        if ($is_res['flag'] == 1) {
            return $this->success($is_res['data']);
        } else {
            return $this->failed($is_res['msg'], $is_res['flag']);
        }
    }


    /**
     * @OA\Post(path="/lunjiu/article/MakeCommentOn",
     *   tags={"酒闻资讯"},
     *   summary="发表评论",
     *   @OA\Parameter(name="api-version", in="header", description="版本号", @OA\Schema(type="string", default="v1")),
     *   @OA\Parameter(name="securitycheckval", in="header", description="token值", @OA\Schema(type="string", default="123456")),
     *   @OA\RequestBody(
     *     @OA\MediaType(
     *       mediaType="multipart/form-data",
     *           @OA\Schema(
     *               @OA\Property(description="资讯id", property="id", type="integer", default="1068"),
     *               @OA\Property(description="评论内容", property="content", type="string", default="毛毛虫化蝶"),
     *               @OA\Property(description="被回复评论的用户id", property="reuid", type="string", default="1"),
     *               @OA\Property(description="被回复的评论内容", property="recontent", type="string", default="毛毛虫化蝶"),
     *               @OA\Property(description="被回复的评论id", property="recomid", type="int", default="2"),
     *               @OA\Property(description="唯一uuid/设备号", property="uuid", type="string", default="521555"),
     *               required={"id","uuid","content"})
     *       )
     *     ),
     *    @OA\Response(
     *            response=200,
     *            description="接口请求成功",
     *            @OA\MediaType(
     *                mediaType="application/json",
     *            @OA\Schema(
     *                 @OA\Property(property="status",
     *                    type="string",
     *                    example="success/fail",
     *                    description="状态"
     *                ),
     *                 @OA\Property(property="errorCode",
     *                    type="string",
     *                    example="0/-1",
     *                    description="状态编码"
     *                ),
     *                 @OA\Property(property="msg",
     *                    type="string",
     *                    example="ok",
     *                    description="状态描述"
     *                ),
     *                @OA\Property(property="data",
     *                   type="object",description="数据",
     *                   @OA\Property(property="list",type="array",description="评论数据",
     *                       @OA\Items(
     *                           type="object",description="",
     *                           @OA\Property(property="comid",type="integer",example="10",description="评论id"),
     *                           @OA\Property(property="replied_comid",type="integer",example="9",description="被回复的评论id"),
     *                           @OA\Property(property="diggnums",type="integer",example="10",description="赞次数"),
     *                           @OA\Property(property="content",type="string",example="上海汪汪汪汪",description="评论内容"),
     *                           @OA\Property(property="addtime",type="integer",example="2020-03-02 12:20:10",description="评论时间"),
     *                           @OA\Property(property="userinfo",type="array",description="用户信息",
     *                               @OA\Items(
     *                                   type="object",description="",
     *                                   @OA\Property(property="uid",type="integer",example="10",description="用户id"),
     *                                   @OA\Property(property="nickname",type="string",example="10",description="昵称"),
     *                                   @OA\Property(property="avatar_image",type="string",example="10",description="头像"),
     *                                   @OA\Property(property="ulevel",type="int",example="10",description="用户等级"),
     *                                   @OA\Property(property="certification_level",type="string",example="WSET3",description="认证等级"),
     *                               ),
     *                           ),
     *                           @OA\Property(property="select_name",type="string",example="毛毛虫",description="被艾特的用户名"),
     *                       ),
     *                   ),
     *               ),
     *            )
     *        ),
     *    )
     * )
     */
    public function MakeCommentOn(Request $request)
    {
        $param  = $request->param();
        $header = $request->header();
        //$is_true = isset($header['securitycheckval']);//判断是否存在token参数
        //$header['securitycheckval'] = $is_true?$header['securitycheckval']:'';//token值
        $ArticleService = new ArticleService();
        $is_res         = $ArticleService->MakeCommentOn($param, $header);
//        var_dump($is_res);
        if ($is_res['flag'] == 1) {
            return $this->success([]);
//            return $this->success(['list'=>$is_res['data']]);
        } else {
            return $this->failed($is_res['msg'], -1);
        }
    }


    
    //todo  新增投票
    public function createVote()
    {

    }


    //todo  更新投票
    public function updateVote()
    {

    }

    //todo  用户投票
    public function userVote()
    {

    }

}
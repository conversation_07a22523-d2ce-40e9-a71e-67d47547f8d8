<?php


namespace app\command;


use app\service\command\CreateArticlesJsCommand;
use think\console\Command;
use think\console\Input;
use think\console\Output;

class CreateArticlesJs extends Command
{
    protected $service;
    protected function configure()
    {
        // 指令配置
        $this->setName('CreateArticlesJs')
            ->setDescription('the CreateArticlesJs command');
    }

    protected function execute(Input $input, Output $output)
    {
        // 指令输出
        $this->init();
        $this->service->exec();
    }
    protected function init(){
        $this->service = new CreateArticlesJsCommand();
    }
}